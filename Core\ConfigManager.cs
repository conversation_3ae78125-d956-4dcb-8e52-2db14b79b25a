using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;
using Microsoft.Win32;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 配置管理类，负责保存和读取窗体状态
    /// </summary>
    public class ConfigManager
    {
        private const string REGISTRY_KEY = @"SOFTWARE\CADFileSaver";
        private static readonly string ConfigPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
            "CADFileSaver", "config.xml");

        /// <summary>
        /// 窗体配置数据
        /// </summary>
        [Serializable]
        public class FormConfig
        {
            public string OrderNumber { get; set; } = "";
            public string MaterialName { get; set; } = "1.5mmABS";
            public string Remark { get; set; } = "";
            public string SerialNumber { get; set; } = "01";
            public string Dimension { get; set; } = "";
            public string BorderColor { get; set; } = "青色";
            public string SavePath { get; set; } = @"C:\Users\<USER>\Desktop";
            public string FileType { get; set; } = "AutoCAD 2007/LT2007 DXF (*.dxf)";
            public List<string> Materials { get; set; } = new List<string>();
        }

        /// <summary>
        /// 保存配置到注册表和文件
        /// </summary>
        public static void SaveConfig(FormConfig config)
        {
            try
            {
                // 保存到注册表
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue("OrderNumber", config.OrderNumber ?? "");
                    key?.SetValue("MaterialName", config.MaterialName ?? "");
                    key?.SetValue("Remark", config.Remark ?? "");
                    key?.SetValue("SerialNumber", config.SerialNumber ?? "01");
                    key?.SetValue("BorderColor", config.BorderColor ?? "青色");
                    key?.SetValue("SavePath", config.SavePath ?? "");
                    key?.SetValue("FileType", config.FileType ?? "");
                }

                // 保存到XML文件
                Directory.CreateDirectory(Path.GetDirectoryName(ConfigPath));
                using (var writer = new StreamWriter(ConfigPath))
                {
                    var serializer = new XmlSerializer(typeof(FormConfig));
                    serializer.Serialize(writer, config);
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"保存配置失败: {ex.Message}", "错误", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从注册表和文件读取配置
        /// </summary>
        public static FormConfig LoadConfig()
        {
            var config = new FormConfig();
            
            try
            {
                // 首先尝试从XML文件读取
                if (File.Exists(ConfigPath))
                {
                    using (var reader = new StreamReader(ConfigPath))
                    {
                        var serializer = new XmlSerializer(typeof(FormConfig));
                        var fileConfig = (FormConfig)serializer.Deserialize(reader);
                        if (fileConfig != null)
                            config = fileConfig;
                    }
                }

                // 然后从注册表读取（覆盖部分设置）
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        config.OrderNumber = key.GetValue("OrderNumber", config.OrderNumber)?.ToString() ?? "";
                        config.MaterialName = key.GetValue("MaterialName", config.MaterialName)?.ToString() ?? "";
                        config.Remark = key.GetValue("Remark", config.Remark)?.ToString() ?? "";
                        config.SerialNumber = key.GetValue("SerialNumber", config.SerialNumber)?.ToString() ?? "01";
                        config.BorderColor = key.GetValue("BorderColor", config.BorderColor)?.ToString() ?? "青色";
                        config.SavePath = key.GetValue("SavePath", config.SavePath)?.ToString() ?? "";
                        config.FileType = key.GetValue("FileType", config.FileType)?.ToString() ?? "";
                    }
                }

                // 智能合并材料列表：保留用户自定义材料，同时添加新的默认材料
                config.Materials = MergeMaterialLists(config.Materials);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"读取配置失败，使用默认配置: {ex.Message}", "警告", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);
                config = new FormConfig { Materials = GetDefaultMaterials() };
            }

            return config;
        }

        /// <summary>
        /// 获取默认材料列表
        /// </summary>
        private static List<string> GetDefaultMaterials()
        {
            return new List<string>
            {
                // ABS材料系列（8种）
                "0.5mmABS", "1.0mmABS", "1.5mmABS", "2.0mmABS",
                "3.0mmABS", "4.0mmABS", "5.0mmABS", "1厘米ABS",

                // 玻璃材料（1种）
                "灰玻璃",

                // 亚克力材料系列（3种）
                "1.7mm亚克力", "3.0mm亚克力", "5.0mm亚克力",

                // 木板材料系列（5种）
                "1.5mm木板", "2.0mm木板", "3.0mm木板", "5.0mm木板", "九厘板",

                // PVC材料系列（3种）
                "8mmPVC", "10mmPVC", "2厘米PVC",

                // 双色板材料系列（4种）
                "红色双色板", "蓝色双色板", "金色双色板", "银色双色板"
            };
        }

        /// <summary>
        /// 智能合并默认材料列表和用户材料列表
        /// 保留用户自定义材料，同时添加新的默认材料（去重）
        /// </summary>
        /// <param name="userMaterials">用户现有材料列表</param>
        /// <returns>合并后的材料列表</returns>
        private static List<string> MergeMaterialLists(List<string> userMaterials)
        {
            try
            {
                var defaultMaterials = GetDefaultMaterials();
                var mergedMaterials = new List<string>();

                // 第一步：保留用户现有材料（优先级最高）
                if (userMaterials != null && userMaterials.Count > 0)
                {
                    foreach (var userMaterial in userMaterials)
                    {
                        if (!string.IsNullOrWhiteSpace(userMaterial))
                        {
                            mergedMaterials.Add(userMaterial.Trim());
                        }
                    }
                }

                // 第二步：添加新的默认材料（避免重复）
                foreach (var defaultMaterial in defaultMaterials)
                {
                    if (!string.IsNullOrWhiteSpace(defaultMaterial))
                    {
                        var trimmedDefault = defaultMaterial.Trim();
                        // 使用忽略大小写的比较来检测重复
                        bool isDuplicate = mergedMaterials.Any(existing =>
                            string.Equals(existing, trimmedDefault, StringComparison.OrdinalIgnoreCase));

                        if (!isDuplicate)
                        {
                            mergedMaterials.Add(trimmedDefault);
                        }
                    }
                }

                return mergedMaterials;
            }
            catch (Exception ex)
            {
                // 如果合并过程出现异常，返回默认材料列表作为备选
                System.Windows.Forms.MessageBox.Show($"材料列表合并时发生错误，使用默认材料列表: {ex.Message}",
                    "警告", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);
                return GetDefaultMaterials();
            }
        }

        /// <summary>
        /// 更新序号（自动递增）
        /// </summary>
        public static string GetNextSerialNumber(string currentSerial)
        {
            try
            {
                if (int.TryParse(currentSerial, out int number))
                {
                    number++;
                    return number.ToString("D2"); // 格式化为两位数字，如01, 02, 10
                }
            }
            catch { }
            
            return "01"; // 默认返回01
        }
    }
}
