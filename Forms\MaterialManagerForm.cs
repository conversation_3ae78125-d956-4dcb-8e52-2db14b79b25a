using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace CADFileSaver.Forms
{
    /// <summary>
    /// 材料管理窗体
    /// </summary>
    public partial class MaterialManagerForm : Form
    {
        public List<string> Materials { get; private set; }

        public MaterialManagerForm(List<string> materials)
        {
            InitializeComponent();

            // 确保窗体置顶，解决被主窗体遮挡的问题
            this.TopMost = true;

            Materials = new List<string>(materials ?? new List<string>());
            LoadMaterials();
        }

        /// <summary>
        /// 加载材料列表
        /// </summary>
        private void LoadMaterials()
        {
            listBoxMaterials.Items.Clear();
            foreach (var material in Materials)
            {
                listBoxMaterials.Items.Add(material);
            }
        }

        /// <summary>
        /// 添加材料
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            var materialName = txtMaterialName.Text.Trim();
            if (string.IsNullOrEmpty(materialName))
            {
                MessageBox.Show("请输入材料名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return;
            }

            if (Materials.Contains(materialName))
            {
                MessageBox.Show("材料名称已存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return;
            }

            Materials.Add(materialName);
            LoadMaterials();
            txtMaterialName.Clear();
            txtMaterialName.Focus();
        }

        /// <summary>
        /// 删除材料
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (listBoxMaterials.SelectedItem == null)
            {
                MessageBox.Show("请选择要删除的材料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedMaterial = listBoxMaterials.SelectedItem.ToString();
            var result = MessageBox.Show($"确定要删除材料 '{selectedMaterial}' 吗？", "确认删除", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Materials.Remove(selectedMaterial);
                LoadMaterials();
            }
        }

        /// <summary>
        /// 编辑材料
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (listBoxMaterials.SelectedItem == null)
            {
                MessageBox.Show("请选择要编辑的材料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedMaterial = listBoxMaterials.SelectedItem.ToString();
            var newName = txtMaterialName.Text.Trim();

            if (string.IsNullOrEmpty(newName))
            {
                MessageBox.Show("请输入新的材料名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return;
            }

            if (newName != selectedMaterial && Materials.Contains(newName))
            {
                MessageBox.Show("材料名称已存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return;
            }

            var index = Materials.IndexOf(selectedMaterial);
            if (index >= 0)
            {
                Materials[index] = newName;
                LoadMaterials();
                txtMaterialName.Clear();
            }
        }

        /// <summary>
        /// 上移材料
        /// </summary>
        private void btnMoveUp_Click(object sender, EventArgs e)
        {
            if (listBoxMaterials.SelectedItem == null || listBoxMaterials.SelectedIndex <= 0)
                return;

            var selectedIndex = listBoxMaterials.SelectedIndex;
            var selectedMaterial = Materials[selectedIndex];

            Materials.RemoveAt(selectedIndex);
            Materials.Insert(selectedIndex - 1, selectedMaterial);

            LoadMaterials();
            listBoxMaterials.SelectedIndex = selectedIndex - 1;
        }

        /// <summary>
        /// 下移材料
        /// </summary>
        private void btnMoveDown_Click(object sender, EventArgs e)
        {
            if (listBoxMaterials.SelectedItem == null || listBoxMaterials.SelectedIndex >= Materials.Count - 1)
                return;

            var selectedIndex = listBoxMaterials.SelectedIndex;
            var selectedMaterial = Materials[selectedIndex];

            Materials.RemoveAt(selectedIndex);
            Materials.Insert(selectedIndex + 1, selectedMaterial);

            LoadMaterials();
            listBoxMaterials.SelectedIndex = selectedIndex + 1;
        }

        /// <summary>
        /// 列表选择改变事件
        /// </summary>
        private void listBoxMaterials_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxMaterials.SelectedItem != null)
            {
                txtMaterialName.Text = listBoxMaterials.SelectedItem.ToString();
            }
        }

        /// <summary>
        /// 文本框回车事件
        /// </summary>
        private void txtMaterialName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnAdd_Click(sender, e);
            }
        }

        /// <summary>
        /// 确定按钮
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
