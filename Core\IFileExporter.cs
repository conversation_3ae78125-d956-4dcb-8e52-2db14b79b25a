using Autodesk.AutoCAD.DatabaseServices;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 文件导出器接口
    /// 定义了文件导出的标准接口，支持不同版本的AutoCAD实现
    /// </summary>
    public interface IFileExporter
    {
        /// <summary>
        /// 保存选择的实体到文件
        /// </summary>
        /// <param name="entityIds">要保存的实体ID集合</param>
        /// <param name="filePath">保存文件的完整路径</param>
        /// <param name="fileType">文件类型（如"AutoCAD 2007/LT2007 DWG (*.dwg)"）</param>
        /// <param name="errorMessage">如果保存失败，返回错误信息</param>
        /// <returns>保存成功返回true，失败返回false</returns>
        bool SaveEntitiesToFile(ObjectIdCollection entityIds, string filePath, 
            string fileType, out string errorMessage);

        /// <summary>
        /// 获取与当前版本兼容的DWG版本
        /// </summary>
        /// <param name="fileType">请求的文件类型</param>
        /// <returns>兼容的DWG版本枚举值</returns>
        DwgVersion GetCompatibleDwgVersion(string fileType);

        /// <summary>
        /// 检查当前实现是否支持当前的AutoCAD版本
        /// </summary>
        /// <returns>支持返回true，不支持返回false</returns>
        bool IsVersionSupported();

        /// <summary>
        /// 获取导出器的名称和版本信息
        /// </summary>
        /// <returns>导出器信息字符串</returns>
        string GetExporterInfo();
    }
}
