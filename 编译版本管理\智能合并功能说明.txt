CAD文件快速保存插件 - 智能材料列表合并功能说明
================================================

版本：v2.0.2.0
更新时间：2025-07-12

功能概述
========

智能材料列表合并功能可以自动将插件的预设材料与用户的自定义材料进行合并，
确保用户既能获得最新的预设材料，又不会丢失任何自定义材料。

工作原理
========

1. 读取用户现有材料列表
   - 从配置文件加载用户之前保存的材料
   - 包括用户手动添加的自定义材料

2. 获取插件预设材料列表
   - 24种最新的预设材料
   - 涵盖ABS、亚克力、木板、PVC、双色板等常用材料

3. 智能合并处理
   - 优先保留用户自定义材料
   - 添加用户列表中没有的预设材料
   - 自动去重，避免重复材料

4. 保存合并结果
   - 将合并后的材料列表保存到配置文件
   - 下次启动时继续使用合并后的列表

合并策略详解
============

优先级规则：
1. 用户自定义材料 > 插件预设材料
2. 已存在的材料 > 新增材料
3. 保留所有有效材料，只去除重复

去重规则：
- 使用忽略大小写的字符串比较
- "1.5mmABS" 和 "1.5mmabs" 被视为相同材料
- 保留第一次出现的材料名称

示例场景
========

场景1：新用户
- 用户材料列表：空
- 合并结果：24种预设材料

场景2：现有用户（有自定义材料）
- 用户材料列表：["自定义材料A", "自定义材料B", "1.5mmABS"]
- 插件预设：24种材料（包含"1.5mmABS"）
- 合并结果：用户的3种材料 + 23种新预设材料（去除重复的"1.5mmABS"）

场景3：现有用户（完全自定义）
- 用户材料列表：["特殊材料1", "特殊材料2", "特殊材料3"]
- 合并结果：用户的3种材料 + 24种预设材料 = 27种材料

技术特性
========

性能优化：
- 合并操作在插件启动时执行一次
- 使用高效的LINQ查询进行去重
- 内存占用极小，处理速度快

异常处理：
- 完善的错误处理机制
- 合并失败时自动使用默认材料列表
- 用户友好的错误提示

数据安全：
- 绝不删除用户自定义材料
- 配置文件自动备份到注册表
- 支持手动恢复和重置

用户体验
========

对现有用户：
✅ 无感知升级 - 重新加载插件后自动获得新材料
✅ 数据保护 - 所有自定义材料完全保留
✅ 功能增强 - 获得24种新的预设材料选择
✅ 操作简单 - 无需任何手动操作

对新用户：
✅ 开箱即用 - 直接获得完整的24种材料预设
✅ 标准体验 - 与升级后的现有用户体验一致

维护说明
========

开发者注意事项：
- 新增预设材料时，只需修改GetDefaultMaterials()方法
- 合并逻辑自动处理新材料的添加
- 支持未来的材料分类和排序功能扩展

用户注意事项：
- 材料列表会在每次启动时自动更新
- 可以通过材料管理功能继续添加自定义材料
- 删除材料时请谨慎，避免误删常用材料

故障排除
========

如果遇到材料列表问题：

1. 检查配置文件
   位置：%APPDATA%\CADFileSaver\config.xml

2. 检查注册表
   位置：HKEY_CURRENT_USER\SOFTWARE\CADFileSaver

3. 重置材料列表
   删除上述配置文件和注册表项，重新启动插件

4. 查看错误日志
   如果出现错误提示，请记录错误信息并联系技术支持

更新历史
========

v2.0.2.0 (2025-07-12)
- 首次实现智能材料列表合并功能
- 支持用户自定义材料保护
- 实现自动去重和合并算法

技术支持
========

如有问题请联系开发者
功能建议和bug报告欢迎反馈
