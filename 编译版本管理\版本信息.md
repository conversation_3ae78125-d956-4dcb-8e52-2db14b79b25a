# CAD文件快速保存插件 v2.0

## 版本信息
- **版本号**: 2.0.3.0
- **编译时间**: 2025-07-13
- **目标框架**: .NET Framework 4.8
- **AutoCAD兼容性**: AutoCAD 2014-2025（双方案自动适配）

## 功能特性

### 核心功能
- ✅ 图形快速保存为DWG/DXF格式
- ✅ 智能文件名生成（订单号-材料名称-备注-序号-尺寸）
- ✅ 自动尺寸计算（基于指定颜色的图形边界）
- ✅ 序号自动递增管理
- ✅ 窗体状态记忆功能

### 界面特性
- ✅ 窗体置顶显示
- ✅ 材料名称管理（增删改查）
- ✅ 多种边框颜色选择（红、蓝、绿、黄、青、洋红）
- ✅ 多种文件格式支持
- ✅ 路径浏览器集成

### 技术特性
- ✅ 基于AutoCAD .NET API开发
- ✅ 支持多种图形类型（直线、多段线、圆弧、圆、椭圆、样条曲线）
- ✅ 智能颜色匹配算法
- ✅ 配置数据持久化（注册表+XML文件）
- ✅ 异常处理和错误提示

## 文件结构
```
编译版本管理/net48/
├── CADFileSaver.dll          # 主插件文件
├── CADFileSaver.pdb          # 调试符号文件
├── AcCoreMgd.dll            # AutoCAD核心管理API
├── AcDbMgd.dll              # AutoCAD数据库管理API
├── AcMgd.dll                # AutoCAD主管理API
└── 其他依赖DLL文件...
```

## 命令列表
| 命令 | 说明 |
|------|------|
| `QUICKSAVE` | 打开快速保存窗体 |
| `快速保存` | 中文命令别名 |
| `QS` | 简短命令别名 |
| `CADFILESAVER_INIT` | 显示插件信息 |

## 使用流程
1. 加载插件到AutoCAD
2. 输入命令打开窗体
3. 配置保存参数
4. 选择CAD图形
5. 自动计算尺寸并保存

## 配置文件位置
- 注册表: `HKEY_CURRENT_USER\SOFTWARE\CADFileSaver`
- XML配置: `%APPDATA%\CADFileSaver\config.xml`

## 更新日志

### v2.0.3.0 (2025-07-13) 🔄 **双方案版本兼容性架构**
- 🏗️ **架构重构**: 实现双方案兼容性设计，支持CAD 2014-2025全版本
- 🔍 **智能版本检测**: 自动检测AutoCAD版本并选择合适的API实现
- 🆕 **现代版本支持**: ModernFileExporter支持CAD 2018+，使用最新API
- 🔧 **传统版本兼容**: LegacyFileExporter支持CAD 2014-2017，使用兼容API
- 🏭 **工厂模式**: FileExporterFactory自动选择合适的导出器实现
- 📊 **兼容性诊断**: 新增CADFILESAVER_COMPAT命令显示版本兼容性信息
- 🛡️ **向下兼容**: 完全保持现有功能，对新版本CAD无任何影响
- 🧹 **代码可维护性**: 清晰的接口设计，易于扩展和维护

### v2.0.2.0 (2025-07-13) 🔧 **第三方工具兼容性修复**
- 🚨 **关键修复**: 移除导致第三方工具识别错误命令的参考代码文件
- 🎯 **命令清理**: 删除 `YOUR_MAIN_COMMAND` 等示例命令，避免工具识别混乱
- 🔧 **兼容性提升**: 确保第三方工具能正确识别 `QUICKSAVE`、`快速保存`、`QS` 等实际命令
- 📁 **项目结构优化**: 将参考代码移至文档文件夹，避免误编译
- ✅ **IP验证保持**: 保留完整的IP地址验证功能（192.168.5.xxx网段限制）
- 🧹 **代码清洁**: 项目结构更清晰，只包含必要的功能代码

### v2.0.1.0 (2025-07-13) ⚡ **性能优化**
- ⚡ **轻量级验证**: 命令方法中使用快速状态检查，提高响应速度
- 🔧 **结构简化**: 减少命令方法中的验证代码，提高第三方工具兼容性
- 🎯 **集中初始化**: 在插件加载时进行完整验证，后续只做状态检查
- 📈 **性能提升**: 避免重复的网络检查和变量声明

### v2.0.0.0 (2025-07-13) 🔒 **安全功能集成**
- 🔒 **IP地址验证**: 只允许192.168.5.xxx网段用户使用插件
- 🛡️ **隐蔽性设计**: 验证失败显示"文件已损坏"，不暴露验证逻辑
- ⚡ **一次性验证**: 只在插件加载时验证一次，后续使用缓存结果
- 🔧 **无侵入集成**: 对现有功能零影响，保持所有原有特性

### v1.9.6.0 (2025-07-12) 🔄 **流程优化**
- 🚀 **连续导出增强**: 更换材料不再退出连续导出模式
- 🔄 **灵活性提升**: 支持在同一会话中使用多种材料
- 📝 **智能提示**: 材料变更时显示友好提示信息
- 🎯 **序号管理**: 材料变更时序号自动重置为01，循环继续
- ⌨️ **简化退出**: 只有按ESC键才退出连续导出模式
- 🧹 **代码优化**: 简化状态管理，移除不必要的变量

### v2.0.5.0 (2025-07-12) 🎨 **界面优化**
- 🖱️ **下拉菜单滚动**: 材料下拉菜单支持鼠标滚轮滚动
- 📏 **智能高度调节**: 根据材料数量自动调整下拉菜单显示高度
- 📱 **响应式设计**: 适配不同屏幕分辨率和窗体位置
- 🎯 **最佳显示**: 少量材料全部显示，大量材料智能分页
- ⚡ **性能优化**: 动态计算显示高度，确保流畅体验
- 🔧 **代码优化**: 添加异常处理，确保功能稳定性

### v2.0.4.0 (2025-07-12) 🚀 **用户体验大幅提升**
- ✨ **连续导出模式**: 实现批量导出，大幅提升工作效率
- 🖥️ **窗体保持显示**: 点击确定后窗体不再隐藏，便于查看和修改设置
- 📝 **命令行提示**: 成功导出后使用CAD命令行提示，不干扰工作流程
- 🔄 **自动循环**: 导出成功后自动进入下一轮，无需重复点击确定
- ⌨️ **智能退出**: 支持ESC键退出或材料变更时自动退出连续模式
- 📊 **导出计数**: 显示已导出文件数量，便于跟踪进度
- 🛡️ **异常优化**: 改进错误处理，所有提示统一使用命令行输出

### v2.0.3.0 (2025-07-12) 🔥 **重要修复**
- 🐛 **修复严重问题**: 保存文件中图形不可见的问题
- 🎯 **核心修复**: WblockCloneObjects目标从BlockTableId改为ModelSpaceId
- ✨ **功能完善**: 确保保存的文件只包含用户选择的图形
- 🔧 **技术改进**: 添加图形范围更新，优化显示效果
- 📝 **代码优化**: 改进异常处理和错误提示
- 🛡️ **质量保证**: 确保保存的DWG/DXF文件完全可用

### v2.0.2.0 (2025-07-12)
- 🎯 实现智能材料列表合并策略
- ✨ 自动合并插件预设材料与用户自定义材料
- 🛡️ 完全保护用户现有自定义材料，不会丢失任何数据
- 🔄 自动去重，避免材料名称重复
- 📈 现有用户无感知升级，自动获得新材料选择
- 🔧 优化材料加载逻辑，提升启动性能
- 📝 完善异常处理，确保材料列表加载的稳定性

### v2.0.1.0 (2025-07-12)
- ✨ 更新默认材料预设列表（24种材料）
- ✨ 新增ABS材料系列（8种规格：0.5mm-5.0mm + 1厘米）
- ✨ 新增亚克力材料系列（3种规格：1.7mm、3.0mm、5.0mm）
- ✨ 新增木板材料系列（5种规格：1.5mm-5.0mm + 九厘板）
- ✨ 新增PVC材料系列（3种规格：8mm、10mm、2厘米）
- ✨ 新增双色板材料系列（4种颜色：红、蓝、金、银）
- ✨ 新增灰玻璃材料
- 📝 优化材料列表分类和注释，提升代码可维护性

### v2.0.0.1 (2025-07-12)
- 🐛 修复材料管理窗体被主窗体遮挡的问题
- ✨ 优化窗体层级管理，确保子窗体正常交互
- 🔧 改进窗体置顶逻辑，临时禁用主窗体TopMost
- 📝 更新使用说明和技术文档

### v2.0.0.0 (2025-07-12)
- 🎉 全新架构设计，提升稳定性和可维护性
- ✨ 新增材料管理功能
- ✨ 新增多种文件格式支持
- ✨ 优化图形长度计算算法
- ✨ 改进窗体界面布局
- 🐛 修复颜色匹配问题
- 🐛 修复文件名特殊字符处理
- 📈 提升整体性能

## 技术架构
- **UI层**: Windows Forms
- **业务层**: 核心逻辑处理
- **数据层**: 配置管理和持久化
- **API层**: AutoCAD .NET API集成

## 开发环境
- Visual Studio 2022
- .NET Framework 4.8
- AutoCAD .NET API 24.2.0
- C# 语言版本: latest

## 兼容性
- ✅ AutoCAD 2018-2025
- ✅ Windows 10/11
- ✅ .NET Framework 4.8+

## 许可证
本软件为定制开发版本，请遵循相关使用协议。
