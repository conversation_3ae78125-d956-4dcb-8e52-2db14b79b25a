using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using CADFileSaver.Core;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 图形操作辅助类
    /// 处理分色功能中的图形复制、移动和计算操作
    /// </summary>
    public static class GraphicsOperationHelper
    {
        /// <summary>
        /// 复制并移动图形实体（带重试机制）
        /// </summary>
        /// <param name="entityIds">要复制的实体ID集合</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <returns>操作是否成功</returns>
        public static bool CopyAndMoveEntities(ObjectIdCollection entityIds, Vector3d displacement, out string errorMessage)
        {
            return CopyAndMoveEntitiesWithRetry(entityIds, displacement, out errorMessage, 3);
        }

        /// <summary>
        /// 复制并移动图形实体（带重试机制的内部实现）
        /// </summary>
        /// <param name="entityIds">要复制的实体ID集合</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns>操作是否成功</returns>
        private static bool CopyAndMoveEntitiesWithRetry(ObjectIdCollection entityIds, Vector3d displacement,
            out string errorMessage, int maxRetries = 3)
        {
            errorMessage = "";

            if (entityIds == null || entityIds.Count == 0)
            {
                errorMessage = "没有要复制的实体";
                return false;
            }

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // 验证所有ObjectId仍然有效（使用外部事务）
                    if (!ValidateObjectIds(entityIds, null))
                    {
                        errorMessage = "部分对象已无效，跳过处理";
                        return false;
                    }

                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    var db = doc.Database;

                    using (var transaction = db.TransactionManager.StartTransaction())
                    {
                        // 获取模型空间
                        var blockTable = transaction.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                        var modelSpace = transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                        // 创建复制的实体集合
                        var copiedEntities = new List<Entity>();

                        // 复制每个实体
                        foreach (ObjectId entityId in entityIds)
                        {
                            try
                            {
                                var originalEntity = transaction.GetObject(entityId, OpenMode.ForRead) as Entity;
                                if (originalEntity != null && !originalEntity.IsDisposed)
                                {
                                    // 创建实体的副本
                                    var copiedEntity = originalEntity.Clone() as Entity;
                                    if (copiedEntity != null)
                                    {
                                        // 应用位移变换
                                        var transformMatrix = Matrix3d.Displacement(displacement);
                                        copiedEntity.TransformBy(transformMatrix);

                                        // 添加到模型空间
                                        modelSpace.AppendEntity(copiedEntity);
                                        transaction.AddNewlyCreatedDBObject(copiedEntity, true);

                                        copiedEntities.Add(copiedEntity);
                                    }
                                }
                            }
                            catch (Autodesk.AutoCAD.Runtime.Exception)
                            {
                                // 单个实体的CAD异常，跳过该实体继续处理其他实体
                                continue;
                            }
                        }

                        // 提交事务
                        transaction.Commit();

                        if (copiedEntities.Count == 0)
                        {
                            errorMessage = "没有成功复制任何实体";
                            return false;
                        }

                        return true;
                    }
                }
                catch (Autodesk.AutoCAD.Runtime.Exception ex)
                {
                    if (attempt < maxRetries)
                    {
                        // 等待一小段时间后重试
                        System.Threading.Thread.Sleep(100 * attempt);
                        continue;
                    }

                    errorMessage = $"CAD对象操作失败，已重试{maxRetries}次: {ex.Message}";
                    return false;
                }
                catch (System.Exception ex)
                {
                    if (attempt < maxRetries)
                    {
                        // 对于其他异常也进行重试
                        System.Threading.Thread.Sleep(50 * attempt);
                        continue;
                    }

                    errorMessage = $"复制移动实体时发生错误(尝试{attempt}次): {ex.Message}";
                    return false;
                }
            }

            errorMessage = "未知错误";
            return false;
        }

        /// <summary>
        /// 计算单位长度（最长线段长度 + 300）
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>单位长度</returns>
        public static double CalculateUnitLength(IEnumerable<Entity> entities)
        {
            try
            {
                if (entities == null || !entities.Any())
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "没有实体用于计算单位长度");
                }

                double maxLength = 0;

                // 遍历所有实体，找出最长的长度
                foreach (var entity in entities)
                {
                    try
                    {
                        double entityLength = GeometryHelper.GetEntityLength(entity);
                        if (entityLength > maxLength)
                        {
                            maxLength = entityLength;
                        }
                    }
                    catch
                    {
                        // 如果某个实体长度计算失败，跳过继续处理其他实体
                        continue;
                    }
                }

                if (maxLength <= 0)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.GraphicsOperationFailed,
                        "无法计算实体长度，所有实体长度都为0或无效");
                }

                // 返回最长长度 + 300
                return maxLength + 300;
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"计算单位长度时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据颜色筛选实体（简化的OR逻辑，模拟AutoLISP）
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="frameColor">外框颜色</param>
        /// <param name="separationColor">分色颜色</param>
        /// <returns>匹配的实体列表</returns>
        public static List<Entity> FilterEntitiesByColor(IEnumerable<Entity> entities,
            string frameColor, string separationColor)
        {
            var matchingEntities = new List<Entity>();

            try
            {
                if (entities == null)
                    return matchingEntities;

                var entityList = entities.ToList();

                // 简化逻辑：实体颜色匹配frameColor OR separationColor就选中（模拟AutoLISP的member函数）
                foreach (var entity in entityList)
                {
                    try
                    {
                        // 简单的OR逻辑：匹配任一颜色就选中
                        if (GeometryHelper.IsColorMatch(entity, frameColor) ||
                            GeometryHelper.IsColorMatch(entity, separationColor))
                        {
                            matchingEntities.Add(entity);
                        }
                    }
                    catch
                    {
                        // 如果某个实体颜色检查失败，跳过继续处理其他实体
                        continue;
                    }
                }

                // 简化的调试信息
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"颜色筛选({frameColor}+{separationColor}): 找到 {matchingEntities.Count} 个匹配图形\n");
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.ColorMatchingFailed,
                    $"按颜色筛选实体时发生错误: {ex.Message}", ex);
            }

            return matchingEntities;
        }

        /// <summary>
        /// 获取实体的边界框信息
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>边界框的最小点和最大点</returns>
        public static (Point3d minPoint, Point3d maxPoint) GetEntitiesBounds(IEnumerable<Entity> entities)
        {
            try
            {
                if (entities == null || !entities.Any())
                {
                    return (Point3d.Origin, Point3d.Origin);
                }

                double minX = double.MaxValue, minY = double.MaxValue, minZ = double.MaxValue;
                double maxX = double.MinValue, maxY = double.MinValue, maxZ = double.MinValue;

                foreach (var entity in entities)
                {
                    try
                    {
                        var bounds = entity.GeometricExtents;
                        
                        minX = Math.Min(minX, bounds.MinPoint.X);
                        minY = Math.Min(minY, bounds.MinPoint.Y);
                        minZ = Math.Min(minZ, bounds.MinPoint.Z);
                        
                        maxX = Math.Max(maxX, bounds.MaxPoint.X);
                        maxY = Math.Max(maxY, bounds.MaxPoint.Y);
                        maxZ = Math.Max(maxZ, bounds.MaxPoint.Z);
                    }
                    catch
                    {
                        // 如果某个实体边界计算失败，跳过继续处理其他实体
                        continue;
                    }
                }

                return (new Point3d(minX, minY, minZ), new Point3d(maxX, maxY, maxZ));
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"计算实体边界时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证实体集合是否有效
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>是否有效</returns>
        public static bool ValidateEntities(IEnumerable<Entity> entities)
        {
            if (entities == null)
                return false;

            try
            {
                return entities.Any() && entities.All(e => e != null && !e.IsDisposed);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建ObjectIdCollection从实体列表
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>ObjectIdCollection</returns>
        public static ObjectIdCollection CreateObjectIdCollection(IEnumerable<Entity> entities)
        {
            var collection = new ObjectIdCollection();

            if (entities != null)
            {
                foreach (var entity in entities)
                {
                    if (entity != null && !entity.IsDisposed)
                    {
                        collection.Add(entity.ObjectId);
                    }
                }
            }

            return collection;
        }

        /// <summary>
        /// 验证ObjectId集合中的对象是否仍然有效（改进版本）
        /// </summary>
        /// <param name="objectIds">ObjectId集合</param>
        /// <param name="transaction">外部事务对象（可选）</param>
        /// <returns>是否所有对象都有效</returns>
        private static bool ValidateObjectIds(ObjectIdCollection objectIds, Transaction transaction = null)
        {
            try
            {
                if (objectIds == null || objectIds.Count == 0)
                    return false;

                bool useExternalTransaction = transaction != null;
                Transaction localTransaction = null;

                try
                {
                    if (!useExternalTransaction)
                    {
                        var doc = AcadApp.DocumentManager.MdiActiveDocument;
                        var db = doc.Database;
                        localTransaction = db.TransactionManager.StartTransaction();
                        transaction = localTransaction;
                    }

                    foreach (ObjectId objectId in objectIds)
                    {
                        // 基本有效性检查
                        if (objectId.IsNull || objectId.IsErased || !objectId.IsValid)
                        {
                            return false;
                        }

                        try
                        {
                            // 尝试以只读模式获取对象
                            var obj = transaction.GetObject(objectId, OpenMode.ForRead);
                            if (obj == null || obj.IsDisposed)
                            {
                                return false;
                            }

                            // 检查对象是否为Entity类型
                            if (!(obj is Entity))
                            {
                                return false;
                            }
                        }
                        catch (Autodesk.AutoCAD.Runtime.Exception ex)
                        {
                            // 记录具体的AutoCAD异常信息
                            System.Diagnostics.Debug.WriteLine($"ObjectId验证失败: {objectId}, 错误: {ex.Message}");
                            return false;
                        }
                        catch (System.Exception ex)
                        {
                            // 记录其他异常信息
                            System.Diagnostics.Debug.WriteLine($"ObjectId验证异常: {objectId}, 错误: {ex.Message}");
                            return false;
                        }
                    }

                    if (!useExternalTransaction)
                    {
                        localTransaction?.Commit();
                    }

                    return true;
                }
                finally
                {
                    if (!useExternalTransaction)
                    {
                        localTransaction?.Dispose();
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ObjectId集合验证失败: {ex.Message}");
                return false;
            }
        }
    }
}
