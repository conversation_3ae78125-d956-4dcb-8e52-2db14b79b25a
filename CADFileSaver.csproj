<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <OutputType>Library</OutputType>
    <AssemblyTitle>CAD文件快速保存插件</AssemblyTitle>
    <AssemblyDescription>CAD图形快速保存DLL插件</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <OutputPath>编译版本管理\</OutputPath>
    <LangVersion>latest</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoCAD.NET" Version="24.2.0" />
    <PackageReference Include="AutoCAD.NET.Core" Version="24.2.0" />
    <PackageReference Include="AutoCAD.NET.Model" Version="24.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Forms\" />
    <Folder Include="Core\" />
    <Folder Include="Resources\" />
    <Folder Include="编译版本管理\" />
  </ItemGroup>

</Project>
