using System.Collections.Generic;
using System.Linq;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 分色功能配置数据类
    /// 包含从UI控件提取的分色配置信息
    /// </summary>
    public class ColorSeparationConfig
    {
        /// <summary>
        /// 外框颜色名称
        /// </summary>
        public string FrameColor { get; set; }

        /// <summary>
        /// 选中的分色项目列表（按选择顺序排列）
        /// </summary>
        public List<string> SeparationColors { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ColorSeparationConfig()
        {
            FrameColor = "青色"; // 默认外框颜色
            SeparationColors = new List<string>();
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            return IsValid(out _);
        }

        /// <summary>
        /// 验证配置是否有效，并返回详细的验证信息
        /// </summary>
        /// <param name="validationMessage">验证消息输出</param>
        /// <returns>配置是否有效</returns>
        public bool IsValid(out string validationMessage)
        {
            validationMessage = "";

            // 定义有效的颜色名称
            var validColors = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

            // 验证外框颜色
            if (string.IsNullOrEmpty(FrameColor))
            {
                validationMessage = "外框颜色不能为空";
                return false;
            }

            if (!validColors.Contains(FrameColor))
            {
                validationMessage = $"外框颜色'{FrameColor}'无效，请选择有效的颜色: {string.Join(", ", validColors)}";
                return false;
            }

            // 验证分色项目
            if (SeparationColors == null || SeparationColors.Count == 0)
            {
                validationMessage = "请至少选择一个分色项目";
                return false;
            }

            // 验证分色项目是否都是有效颜色
            foreach (var color in SeparationColors)
            {
                if (string.IsNullOrEmpty(color) || !validColors.Contains(color))
                {
                    validationMessage = $"分色颜色'{color}'无效，请选择有效的颜色: {string.Join(", ", validColors)}";
                    return false;
                }
            }

            // 检查是否有重复的分色项目
            var duplicates = SeparationColors.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
            if (duplicates.Any())
            {
                validationMessage = $"分色项目中有重复的颜色: {string.Join(", ", duplicates)}";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取配置描述信息
        /// </summary>
        /// <returns>配置描述字符串</returns>
        public string GetDescription()
        {
            if (!IsValid())
                return "无效配置";

            return $"外框颜色: {FrameColor}, 分色项目: [{string.Join(", ", SeparationColors)}]";
        }
    }

    /// <summary>
    /// 颜色组合信息类
    /// 表示外框颜色与分色颜色的组合
    /// </summary>
    public class ColorCombination
    {
        /// <summary>
        /// 外框颜色名称
        /// </summary>
        public string FrameColor { get; set; }

        /// <summary>
        /// 分色颜色名称
        /// </summary>
        public string SeparationColor { get; set; }

        /// <summary>
        /// 外框颜色索引
        /// </summary>
        public short FrameColorIndex { get; set; }

        /// <summary>
        /// 分色颜色索引
        /// </summary>
        public short SeparationColorIndex { get; set; }

        /// <summary>
        /// 处理顺序（从1开始）
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 获取组合描述
        /// </summary>
        /// <returns>组合描述字符串</returns>
        public string GetDescription()
        {
            return $"{FrameColor}+{SeparationColor} (第{Order}组)";
        }
    }

    /// <summary>
    /// 分色处理结果类
    /// 包含分色操作的执行结果信息
    /// </summary>
    public class SeparationResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 已处理的颜色组合数量
        /// </summary>
        public int ProcessedCombinations { get; set; }

        /// <summary>
        /// 处理的图形实体总数
        /// </summary>
        public int TotalEntities { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SeparationResult()
        {
            Success = false;
            Message = "";
            ProcessedCombinations = 0;
            TotalEntities = 0;
            ErrorMessage = "";
        }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="processedCount">处理的组合数量</param>
        /// <param name="totalEntities">总实体数量</param>
        /// <returns>成功结果对象</returns>
        public static SeparationResult CreateSuccess(int processedCount, int totalEntities)
        {
            return new SeparationResult
            {
                Success = true,
                ProcessedCombinations = processedCount,
                TotalEntities = totalEntities,
                Message = $"竖向分色完成，共处理 {processedCount} 个颜色组合，涉及 {totalEntities} 个图形实体"
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>失败结果对象</returns>
        public static SeparationResult CreateFailure(string errorMessage)
        {
            return new SeparationResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                Message = $"竖向分色失败: {errorMessage}"
            };
        }
    }
}
