CAD文件快速保存插件 - 连续导出模式功能说明
==========================================

版本：v2.0.4.0
更新时间：2025-07-12

🚀 连续导出模式 - 大幅提升批量导出效率
=====================================

功能概述
========

连续导出模式是v2.0.4版本的重要新功能，专为批量导出设计，
让用户可以连续导出多个文件，无需重复操作，大幅提升工作效率。

核心特性
========

1. 🖥️ **窗体保持显示**
   - 点击确定后窗体不再隐藏
   - 用户可以随时查看和修改设置
   - 保持操作的连续性和可视性

2. 🔄 **自动循环导出**
   - 成功导出后自动进入下一轮
   - 无需重复点击确定按钮
   - 序号自动递增，准备下一次导出

3. 📝 **命令行提示**
   - 所有提示信息显示在CAD命令行
   - 不再弹出干扰性的对话框
   - 保持工作流程的流畅性

4. ⌨️ **智能退出机制**
   - 按ESC键随时退出连续模式
   - 更换材料时自动退出（序号重置）
   - 发生错误时自动退出

5. 📊 **进度跟踪**
   - 显示当前是第几次导出
   - 统计已完成的导出数量
   - 便于跟踪批量导出进度

操作流程
========

1. **启动连续模式**
   - 填写窗体信息（订单号、材料、备注等）
   - 点击"确定"按钮
   - 系统提示"开始连续导出模式"

2. **批量导出循环**
   - 在CAD中选择要保存的图形
   - 按空格键确认选择
   - 系统自动计算尺寸、生成文件名、保存文件
   - 命令行显示导出成功信息
   - 序号自动递增
   - 重新提示选择下一批图形

3. **退出连续模式**
   - 按ESC键主动退出
   - 更换材料时自动退出
   - 关闭窗体时自动退出
   - 发生错误时自动退出

命令行提示信息
==============

启动提示：
```
=== 开始连续导出模式 ===
提示：按ESC退出或更换材料结束连续导出
```

导出过程：
```
[第1次导出] 请选择要保存的图形，完成后按空格键...
文件已成功导出到指定文件夹: C:\Users\<USER>\
文件名: FY845-1.5mmABS-01-980x550.dxf
已完成 1 个文件的导出

[第2次导出] 请选择要保存的图形，完成后按空格键...
文件已成功导出到指定文件夹: C:\Users\<USER>\
文件名: FY845-1.5mmABS-02-750x400.dxf
已完成 2 个文件的导出
```

退出提示：
```
用户按ESC取消，退出连续导出模式
=== 连续导出模式结束，共导出 2 个文件 ===
```

使用场景
========

连续导出模式特别适用于以下场景：

1. **批量生产订单**
   - 同一订单需要导出多个不同尺寸的文件
   - 材料相同，只是图形和尺寸不同

2. **标准化作业**
   - 重复性的图形导出工作
   - 需要保持一致的文件命名规范

3. **效率优化**
   - 减少重复操作，提升工作效率
   - 避免频繁的窗体操作和弹窗干扰

操作技巧
========

1. **材料管理**
   - 在开始批量导出前，确保材料选择正确
   - 如需更换材料，直接在下拉框中选择即可自动退出

2. **序号管理**
   - 序号会自动递增（01→02→03...）
   - 材料变更时序号自动重置为01

3. **路径管理**
   - 建议在开始前设置好保存路径
   - 连续导出过程中路径保持不变

4. **错误处理**
   - 如果某次导出失败，系统会自动退出连续模式
   - 检查错误信息，修正后重新开始

注意事项
========

1. **图形选择**
   - 确保选择的图形包含指定颜色的边框
   - 边框颜色用于计算尺寸，必须正确设置

2. **文件覆盖**
   - 如果文件名重复，新文件会覆盖旧文件
   - 建议使用不同的序号或备注来区分

3. **系统资源**
   - 长时间连续导出时注意系统资源
   - 如有异常可以按ESC退出重新开始

4. **数据备份**
   - 重要文件建议及时备份
   - 配置信息会自动保存

性能优化
========

连续导出模式在性能方面的优化：

1. **内存管理**
   - 每次导出后自动释放临时资源
   - 避免长时间运行导致的内存泄漏

2. **界面响应**
   - 窗体保持响应，可以随时修改设置
   - 不阻塞CAD主界面的操作

3. **错误恢复**
   - 单次导出失败不影响整体功能
   - 自动错误恢复和状态重置

故障排除
========

常见问题及解决方案：

1. **无法进入连续模式**
   - 检查窗体信息是否填写完整
   - 确认CAD文档已打开

2. **导出失败**
   - 检查保存路径是否有效
   - 确认选择的图形包含指定颜色边框

3. **无法退出连续模式**
   - 按ESC键强制退出
   - 关闭窗体重新打开

4. **序号不递增**
   - 检查是否成功导出
   - 确认材料没有变更

更新历史
========

v2.0.4.0 (2025-07-12)
- 首次实现连续导出模式
- 支持批量导出和自动循环
- 优化用户体验和工作流程

技术支持
========

如有问题请联系开发者
功能建议和使用反馈欢迎提供

连续导出模式让批量导出工作变得简单高效！
