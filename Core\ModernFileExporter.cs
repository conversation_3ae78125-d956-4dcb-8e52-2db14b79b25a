using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 现代版本文件导出器（AutoCAD 2018及以上版本）
    /// 使用最新的AutoCAD .NET API实现文件导出功能
    /// </summary>
    public class ModernFileExporter : IFileExporter
    {
        /// <summary>
        /// 支持的文件格式映射（现代版本）
        /// </summary>
        private static readonly Dictionary<string, DwgVersion> ModernFileFormatMapping = new Dictionary<string, DwgVersion>
        {
            { "AutoCAD 2018 DWG (*.dwg)", DwgVersion.Newest },
            { "AutoCAD 2013 DWG (*.dwg)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DWG (*.dwg)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DWG (*.dwg)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DWG (*.dwg)", DwgVersion.AC1014 },
            { "AutoCAD 2018 DXF (*.dxf)", DwgVersion.Newest },
            { "AutoCAD 2013 DXF (*.dxf)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DXF (*.dxf)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DXF (*.dxf)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DXF (*.dxf)", DwgVersion.AC1014 }
        };

        /// <summary>
        /// 保存选择的实体到文件（现代版本实现）
        /// </summary>
        public bool SaveEntitiesToFile(ObjectIdCollection entityIds, string filePath, 
            string fileType, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;

                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    // 创建临时数据库
                    using (var tempDb = new Database(true, true))
                    {
                        // 使用现代API：获取临时数据库的模型空间ID
                        var modelSpaceId = SymbolUtilityServices.GetBlockModelSpaceId(tempDb);

                        // 复制实体到临时数据库的模型空间
                        var idMapping = new IdMapping();
                        db.WblockCloneObjects(entityIds, modelSpaceId, idMapping,
                            DuplicateRecordCloning.Replace, false);

                        // 更新图形范围，确保正确的显示边界
                        tempDb.UpdateExt(false);

                        // 确保目录存在
                        var directory = Path.GetDirectoryName(filePath);
                        if (!Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }

                        // 保存文件
                        var dwgVersion = GetCompatibleDwgVersion(fileType);
                        var extension = GetFileExtension(fileType);

                        if (extension == ".dxf")
                        {
                            tempDb.DxfOut(filePath, 16, dwgVersion);
                        }
                        else
                        {
                            tempDb.SaveAs(filePath, dwgVersion);
                        }
                    }

                    transaction.Commit();
                }

                return true;
            }
            catch (System.Exception ex)
            {
                errorMessage = $"现代版本导出失败: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 获取与当前版本兼容的DWG版本
        /// </summary>
        public DwgVersion GetCompatibleDwgVersion(string fileType)
        {
            if (ModernFileFormatMapping.ContainsKey(fileType))
                return ModernFileFormatMapping[fileType];
            
            return DwgVersion.AC1021; // 默认AutoCAD 2007
        }

        /// <summary>
        /// 检查当前实现是否支持当前的AutoCAD版本
        /// </summary>
        public bool IsVersionSupported()
        {
            return VersionDetector.IsModernVersion();
        }

        /// <summary>
        /// 获取导出器的名称和版本信息
        /// </summary>
        public string GetExporterInfo()
        {
            return $"ModernFileExporter for {VersionDetector.GetVersionInfo()}";
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="fileType">文件类型</param>
        /// <returns>文件扩展名</returns>
        private string GetFileExtension(string fileType)
        {
            if (fileType.Contains("DWG"))
                return ".dwg";
            else if (fileType.Contains("DXF"))
                return ".dxf";
            else
                return ".dwg"; // 默认
        }

        #endregion
    }
}
