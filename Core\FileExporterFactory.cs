using System;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 文件导出器工厂类
    /// 根据当前AutoCAD版本自动选择合适的文件导出器实现
    /// </summary>
    public static class FileExporterFactory
    {
        private static IFileExporter _cachedExporter = null;
        private static readonly object _lock = new object();

        /// <summary>
        /// 创建适合当前AutoCAD版本的文件导出器
        /// </summary>
        /// <returns>文件导出器实例</returns>
        public static IFileExporter CreateExporter()
        {
            // 使用双重检查锁定模式确保线程安全的单例
            if (_cachedExporter == null)
            {
                lock (_lock)
                {
                    if (_cachedExporter == null)
                    {
                        _cachedExporter = CreateExporterInternal();
                    }
                }
            }

            return _cachedExporter;
        }

        /// <summary>
        /// 强制重新创建导出器（用于版本变更或测试）
        /// </summary>
        /// <returns>新的文件导出器实例</returns>
        public static IFileExporter RecreateExporter()
        {
            lock (_lock)
            {
                _cachedExporter = null;
                VersionDetector.ResetCache(); // 重置版本检测缓存
                return CreateExporter();
            }
        }

        /// <summary>
        /// 获取当前使用的导出器信息
        /// </summary>
        /// <returns>导出器信息字符串</returns>
        public static string GetCurrentExporterInfo()
        {
            var exporter = CreateExporter();
            return exporter.GetExporterInfo();
        }

        /// <summary>
        /// 检查当前导出器是否支持当前版本
        /// </summary>
        /// <returns>支持返回true，不支持返回false</returns>
        public static bool IsCurrentExporterSupported()
        {
            try
            {
                var exporter = CreateExporter();
                return exporter.IsVersionSupported();
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取版本兼容性报告
        /// </summary>
        /// <returns>兼容性报告字符串</returns>
        public static string GetCompatibilityReport()
        {
            try
            {
                var versionInfo = VersionDetector.GetVersionInfo();
                var isLegacy = VersionDetector.IsLegacyVersion();
                var isModern = VersionDetector.IsModernVersion();
                var exporterInfo = GetCurrentExporterInfo();
                var isSupported = IsCurrentExporterSupported();

                return $"版本检测: {versionInfo}\n" +
                       $"传统版本: {isLegacy}\n" +
                       $"现代版本: {isModern}\n" +
                       $"导出器: {exporterInfo}\n" +
                       $"支持状态: {(isSupported ? "支持" : "不支持")}";
            }
            catch (Exception ex)
            {
                return $"兼容性检查失败: {ex.Message}";
            }
        }

        #region 私有方法

        /// <summary>
        /// 内部创建导出器的实现
        /// </summary>
        /// <returns>文件导出器实例</returns>
        private static IFileExporter CreateExporterInternal()
        {
            try
            {
                // 根据版本检测结果选择合适的导出器
                if (VersionDetector.IsLegacyVersion())
                {
                    return new LegacyFileExporter();
                }
                else if (VersionDetector.IsModernVersion())
                {
                    return new ModernFileExporter();
                }
                else
                {
                    // 对于未知版本，默认尝试现代版本实现
                    // 如果失败，会在使用时抛出异常
                    return new ModernFileExporter();
                }
            }
            catch (Exception ex)
            {
                // 如果创建失败，抛出详细的异常信息
                throw new InvalidOperationException(
                    $"无法为当前AutoCAD版本创建合适的文件导出器。" +
                    $"版本信息: {VersionDetector.GetVersionInfo()}。" +
                    $"错误详情: {ex.Message}", ex);
            }
        }

        #endregion

        #region 调试和测试方法

        /// <summary>
        /// 强制使用指定类型的导出器（仅用于测试）
        /// </summary>
        /// <param name="exporterType">导出器类型</param>
        /// <returns>指定类型的导出器实例</returns>
        internal static IFileExporter CreateExporterForTesting(ExporterType exporterType)
        {
            switch (exporterType)
            {
                case ExporterType.Legacy:
                    return new LegacyFileExporter();
                case ExporterType.Modern:
                    return new ModernFileExporter();
                default:
                    throw new ArgumentException($"不支持的导出器类型: {exporterType}");
            }
        }

        /// <summary>
        /// 导出器类型枚举（用于测试）
        /// </summary>
        internal enum ExporterType
        {
            Legacy,
            Modern
        }

        #endregion
    }
}
