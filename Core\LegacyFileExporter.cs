using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 传统版本文件导出器（AutoCAD 2014-2017）
    /// 使用兼容的AutoCAD .NET API实现文件导出功能
    /// </summary>
    public class LegacyFileExporter : IFileExporter
    {
        /// <summary>
        /// 支持的文件格式映射（传统版本兼容）
        /// </summary>
        private static readonly Dictionary<string, DwgVersion> LegacyFileFormatMapping = new Dictionary<string, DwgVersion>
        {
            // 传统版本不使用DwgVersion.Newest，使用具体版本
            { "AutoCAD 2018 DWG (*.dwg)", DwgVersion.AC1032 }, // 使用具体版本而非Newest
            { "AutoCAD 2013 DWG (*.dwg)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DWG (*.dwg)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DWG (*.dwg)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DWG (*.dwg)", DwgVersion.AC1014 },
            { "AutoCAD 2018 DXF (*.dxf)", DwgVersion.AC1032 },
            { "AutoCAD 2013 DXF (*.dxf)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DXF (*.dxf)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DXF (*.dxf)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DXF (*.dxf)", DwgVersion.AC1014 }
        };

        /// <summary>
        /// 保存选择的实体到文件（传统版本兼容实现）
        /// </summary>
        public bool SaveEntitiesToFile(ObjectIdCollection entityIds, string filePath, 
            string fileType, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;

                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    // 创建临时数据库（使用兼容的构造函数参数）
                    using (var tempDb = new Database(false, true))
                    {
                        // 使用传统API：通过块表获取模型空间ID
                        ObjectId modelSpaceId = GetModelSpaceIdLegacy(tempDb, transaction);

                        // 复制实体到临时数据库的模型空间
                        var idMapping = new IdMapping();
                        db.WblockCloneObjects(entityIds, modelSpaceId, idMapping,
                            DuplicateRecordCloning.Replace, false);

                        // 更新图形范围（传统版本兼容方式）
                        try
                        {
                            tempDb.UpdateExt(false);
                        }
                        catch (System.Exception)
                        {
                            // 某些老版本可能不支持此方法，忽略错误
                        }

                        // 确保目录存在
                        var directory = Path.GetDirectoryName(filePath);
                        if (!Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }

                        // 保存文件（使用兼容的方法）
                        var dwgVersion = GetCompatibleDwgVersion(fileType);
                        var extension = GetFileExtension(fileType);

                        if (extension == ".dxf")
                        {
                            // 传统版本的DXF导出（可能需要不同的参数）
                            SaveAsDxfLegacy(tempDb, filePath, dwgVersion);
                        }
                        else
                        {
                            // 传统版本的DWG保存
                            tempDb.SaveAs(filePath, dwgVersion);
                        }
                    }

                    transaction.Commit();
                }

                return true;
            }
            catch (System.Exception ex)
            {
                errorMessage = $"传统版本导出失败: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 获取与当前版本兼容的DWG版本
        /// </summary>
        public DwgVersion GetCompatibleDwgVersion(string fileType)
        {
            if (LegacyFileFormatMapping.ContainsKey(fileType))
                return LegacyFileFormatMapping[fileType];
            
            // 传统版本默认使用较老的格式以确保兼容性
            return DwgVersion.AC1021; // 默认AutoCAD 2007
        }

        /// <summary>
        /// 检查当前实现是否支持当前的AutoCAD版本
        /// </summary>
        public bool IsVersionSupported()
        {
            return VersionDetector.IsLegacyVersion();
        }

        /// <summary>
        /// 获取导出器的名称和版本信息
        /// </summary>
        public string GetExporterInfo()
        {
            return $"LegacyFileExporter for {VersionDetector.GetVersionInfo()}";
        }

        #region 私有兼容性方法

        /// <summary>
        /// 使用传统方法获取模型空间ID
        /// </summary>
        /// <param name="database">数据库对象</param>
        /// <param name="transaction">事务对象</param>
        /// <returns>模型空间的ObjectId</returns>
        private ObjectId GetModelSpaceIdLegacy(Database database, Transaction transaction)
        {
            try
            {
                // 传统方法：通过块表获取模型空间
                var blockTable = (BlockTable)transaction.GetObject(database.BlockTableId, OpenMode.ForRead);
                return blockTable[BlockTableRecord.ModelSpace];
            }
            catch (System.Exception)
            {
                // 如果传统方法失败，尝试使用更基础的方法
                return database.CurrentSpaceId;
            }
        }

        /// <summary>
        /// 传统版本的DXF保存方法
        /// </summary>
        /// <param name="database">数据库对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="dwgVersion">DWG版本</param>
        private void SaveAsDxfLegacy(Database database, string filePath, DwgVersion dwgVersion)
        {
            try
            {
                // 尝试使用标准的DxfOut方法
                database.DxfOut(filePath, 16, dwgVersion);
            }
            catch (System.Exception)
            {
                try
                {
                    // 如果标准方法失败，尝试使用简化参数
                    database.DxfOut(filePath, 16, DwgVersion.AC1021);
                }
                catch (System.Exception)
                {
                    // 最后的兼容性尝试：保存为DWG然后转换
                    var tempDwgPath = Path.ChangeExtension(filePath, ".dwg");
                    database.SaveAs(tempDwgPath, dwgVersion);
                    
                    // 注意：这里可能需要额外的转换逻辑
                    // 在实际应用中，可能需要使用AutoCAD的命令行工具进行转换
                    throw new NotSupportedException("传统版本DXF导出需要额外的转换步骤");
                }
            }
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="fileType">文件类型</param>
        /// <returns>文件扩展名</returns>
        private string GetFileExtension(string fileType)
        {
            if (fileType.Contains("DWG"))
                return ".dwg";
            else if (fileType.Contains("DXF"))
                return ".dxf";
            else
                return ".dwg"; // 默认
        }

        #endregion
    }
}
