CAD文件快速保存插件 v2.0 使用说明
=====================================

文件说明：
- CADFileSaver.dll：主插件文件，需要加载到AutoCAD中
- 其他DLL文件：AutoCAD API依赖文件，需要与主插件放在同一目录

安装方法：
1. 将整个net48文件夹复制到您的插件目录
2. 在AutoCAD中使用NETLOAD命令加载CADFileSaver.dll
3. 或者将插件路径添加到AutoCAD的启动配置中

使用方法：
1. 在AutoCAD命令行输入以下任一命令：
   - QUICKSAVE
   - 快速保存
   - QS

2. 插件窗体说明：
   - 订单号：可选，用于文件名前缀
   - 材料名称：必选，从下拉列表选择，可通过右侧按钮管理
   - 备注内容：可选，用于文件名
   - 序号：默认01，成功保存后自动递增，材料变更时重置
   - 尺寸：自动计算，显示最长边x最短边
   - 边框颜色：选择要计算尺寸的图形颜色（红、蓝、绿、黄、青、洋红）
   - 文件存储路径：选择保存位置
   - 文件类型：选择保存格式（DWG或DXF）

3. 工作流程：
   - 填写窗体信息
   - 点击"确定"进入连续导出模式
   - 窗体保持显示，在CAD中选择要保存的图形
   - 按空格键完成选择，插件自动计算尺寸并保存文件
   - 成功后自动进入下一轮，可继续选择图形导出
   - 可随时更换材料，序号自动重置，循环继续
   - 只有按ESC键才退出连续导出模式

功能特点：
- 窗体置顶，不被其他窗口遮挡
- 材料管理窗体层级优化，确保正常交互
- 智能材料列表合并，保护用户自定义材料同时添加新预设
- 材料下拉菜单优化，支持滚轮滚动，自动调节高度
- 精确图形保存，确保保存文件只包含选择的图形
- 连续导出模式，大幅提升批量导出效率
- 命令行提示，不干扰工作流程
- 记忆功能，保存上次使用的设置
- 自动序号递增
- 支持多种CAD文件格式
- 智能文件名生成

文件名规则：
订单号-材料名称-备注内容-序号-尺寸
（空白项目会自动跳过）

示例：
FY845-1.5mmABS-02-980x550.dxf

注意事项：
- 确保选择的图形中包含指定颜色的边框
- 路径中不要包含特殊字符
- 建议定期备份配置文件
- 保存的文件将只包含您选择的图形，不包含其他内容

预设材料列表（24种）：
ABS系列：0.5mm、1.0mm、1.5mm、2.0mm、3.0mm、4.0mm、5.0mm、1厘米
玻璃系列：灰玻璃
亚克力系列：1.7mm、3.0mm、5.0mm
木板系列：1.5mm、2.0mm、3.0mm、5.0mm、九厘板
PVC系列：8mm、10mm、2厘米
双色板系列：红色、蓝色、金色、银色

材料列表更新说明：
- 现有用户：自动合并新材料到现有列表，保留所有自定义材料
- 新用户：直接获得完整的24种预设材料
- 重复处理：相同名称的材料只保留一个，避免重复
- 更新时机：每次启动插件时自动执行合并

流程优化说明：
v2.0.6版本优化连续导出流程，提升操作灵活性
- 更换材料不再退出连续导出模式
- 材料变更时序号自动重置为01，循环继续
- 支持在同一会话中使用多种材料
- 只有按ESC键才退出连续导出模式

界面优化说明：
v2.0.5版本优化材料下拉菜单显示效果
- 下拉菜单支持滚轮滚动浏览
- 自动调节显示高度，避免菜单过长
- 根据材料数量智能调整显示项目数
- 适配不同屏幕分辨率和窗体位置

连续导出模式说明：
v2.0.4版本新增连续导出模式，大幅提升批量导出效率
- 点击确定后窗体保持显示
- 成功导出后自动进入下一轮
- 使用命令行提示，不干扰工作流程

重要修复说明：
v2.0.3版本修复了保存文件中图形不可见的严重问题
现在保存的文件将正确显示您选择的所有图形

技术支持：
如有问题请联系开发者
版本：v2.0.6
编译时间：2025-07-12
