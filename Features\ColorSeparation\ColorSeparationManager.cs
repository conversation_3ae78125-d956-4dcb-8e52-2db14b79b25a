using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using CADFileSaver.Core;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 分色功能管理器
    /// 负责协调整个竖向分色功能的执行流程
    /// </summary>
    public class ColorSeparationManager
    {


        #region 私有字段

        /// <summary>
        /// 外框颜色下拉框引用
        /// </summary>
        private readonly ComboBox _cmbFrameColor;

        /// <summary>
        /// 分色项目复选框引用
        /// </summary>
        private readonly CheckBox[] _colorCheckBoxes;

        /// <summary>
        /// 默认批处理大小（可根据实体类型动态调整）
        /// </summary>
        private const int DEFAULT_BATCH_SIZE = 200;

        /// <summary>
        /// 最大批处理大小
        /// </summary>
        private const int MAX_BATCH_SIZE = 1000;

        /// <summary>
        /// 最小批处理大小
        /// </summary>
        private const int MIN_BATCH_SIZE = 10;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cmbFrameColor">外框颜色下拉框</param>
        /// <param name="chkRed">红色复选框</param>
        /// <param name="chkBlue">蓝色复选框</param>
        /// <param name="chkGreen">绿色复选框</param>
        /// <param name="chkYellow">黄色复选框</param>
        /// <param name="chkCyan">青色复选框</param>
        /// <param name="chkMagenta">洋红复选框</param>
        public ColorSeparationManager(ComboBox cmbFrameColor, CheckBox chkRed, CheckBox chkBlue, 
            CheckBox chkGreen, CheckBox chkYellow, CheckBox chkCyan, CheckBox chkMagenta)
        {
            _cmbFrameColor = cmbFrameColor ?? throw new ArgumentNullException(nameof(cmbFrameColor));
            
            _colorCheckBoxes = new CheckBox[]
            {
                chkRed ?? throw new ArgumentNullException(nameof(chkRed)),
                chkBlue ?? throw new ArgumentNullException(nameof(chkBlue)),
                chkGreen ?? throw new ArgumentNullException(nameof(chkGreen)),
                chkYellow ?? throw new ArgumentNullException(nameof(chkYellow)),
                chkCyan ?? throw new ArgumentNullException(nameof(chkCyan)),
                chkMagenta ?? throw new ArgumentNullException(nameof(chkMagenta))
            };
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行竖向分色操作
        /// </summary>
        /// <returns>分色操作结果</returns>
        public SeparationResult ExecuteVerticalColorSeparation()
        {
            try
            {
                // 第一阶段：提取配置信息
                var config = ExtractConfigurationFromUI();
                if (!config.IsValid(out string validationMessage))
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.InvalidColorSelection,
                        validationMessage);
                }

                // 第二阶段：用户交互
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc.Editor;

                UserInteractionHelper.ShowOperationStart(editor, config);

                // 获取用户选择的图形
                var selectionSet = UserInteractionHelper.GetUserSelection(editor, out var status);
                
                if (status == PromptStatus.Cancel)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.UserCancelled,
                        "用户取消操作");
                }

                if (status != PromptStatus.OK || selectionSet == null)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "未选择任何图形实体");
                }

                // 第三阶段：处理选择的图形
                return ProcessSelectedEntities(selectionSet, config, editor);
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"执行竖向分色时发生未预期的错误: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从UI控件提取分色配置信息
        /// </summary>
        /// <returns>分色配置对象</returns>
        private ColorSeparationConfig ExtractConfigurationFromUI()
        {
            var config = new ColorSeparationConfig();

            try
            {
                // 定义有效的颜色名称
                var validColors = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                // 提取外框颜色 - 修复：确保获取的是实际颜色值
                var selectedFrameColor = _cmbFrameColor.SelectedItem?.ToString();

                if (!string.IsNullOrEmpty(selectedFrameColor) && validColors.Contains(selectedFrameColor))
                {
                    config.FrameColor = selectedFrameColor;
                }
                else
                {
                    // 如果选择的不是有效颜色，使用默认值
                    config.FrameColor = "青色";

                    // 记录警告信息
                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    doc?.Editor.WriteMessage($"\n⚠ 警告: 外框颜色选择无效('{selectedFrameColor}')，使用默认颜色'青色'\n");
                }

                // 按固定顺序提取选中的分色项目
                var colorNames = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                for (int i = 0; i < _colorCheckBoxes.Length && i < colorNames.Length; i++)
                {
                    if (_colorCheckBoxes[i].Checked)
                    {
                        config.SeparationColors.Add(colorNames[i]);
                    }
                }

                return config;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"提取UI配置时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成颜色组合列表
        /// </summary>
        /// <param name="config">分色配置</param>
        /// <returns>颜色组合列表</returns>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            try
            {
                for (int i = 0; i < config.SeparationColors.Count; i++)
                {
                    var combination = new ColorCombination
                    {
                        FrameColor = config.FrameColor,
                        SeparationColor = config.SeparationColors[i],
                        FrameColorIndex = GetColorIndex(config.FrameColor),
                        SeparationColorIndex = GetColorIndex(config.SeparationColors[i]),
                        Order = i + 1
                    };
                    combinations.Add(combination);
                }

                return combinations;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"生成颜色组合时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取颜色索引
        /// </summary>
        /// <param name="colorName">颜色名称</param>
        /// <returns>颜色索引</returns>
        private short GetColorIndex(string colorName)
        {
            var colorMapping = new Dictionary<string, short>
            {
                { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
            };

            return colorMapping.ContainsKey(colorName) ? colorMapping[colorName] : (short)7;
        }

        /// <summary>
        /// 处理选择的实体（统一事务管理）
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="config">分色配置</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessSelectedEntities(SelectionSet selectionSet,
            ColorSeparationConfig config, Editor editor)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;

                // 统一事务管理：只在最外层开启一个事务
                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // 提取实体
                        var entities = UserInteractionHelper.GetEntitiesFromSelection(selectionSet, transaction);

                        if (!UserInteractionHelper.ValidateSelectedEntities(entities, editor))
                        {
                            throw new ColorSeparationException(
                                ColorSeparationException.ErrorType.NoEntitiesSelected,
                                "选择的实体无效或为空");
                        }

                        // 计算单位长度
                        double unitLength = GraphicsOperationHelper.CalculateUnitLength(entities);
                        UserInteractionHelper.ShowDebugInfo(editor, $"计算得到的单位长度: {unitLength:F2}");

                        // 生成颜色组合
                        var combinations = GenerateColorCombinations(config);

                        // 执行分色处理（传递事务对象，避免嵌套）
                        var result = ProcessColorSeparationWithTransaction(entities, combinations, unitLength, editor, transaction, db);

                        // 提交事务
                        transaction.Commit();

                        // 显示结果
                        UserInteractionHelper.ShowResult(editor, result);

                        return result;
                    }
                    catch (System.Exception)
                    {
                        // 异常时自动回滚事务
                        transaction.Abort();
                        throw;
                    }
                }
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.DatabaseOperationFailed,
                    $"处理选择的实体时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理分色操作（统一使用优化的复制粘贴方式）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="combinations">颜色组合列表</param>
        /// <param name="unitLength">单位长度</param>
        /// <param name="editor">编辑器</param>
        /// <param name="transaction">外层事务对象</param>
        /// <param name="database">数据库对象</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessColorSeparationWithTransaction(List<Entity> entities,
            List<ColorCombination> combinations, double unitLength, Editor editor,
            Transaction transaction, Database database)
        {
            try
            {
                // 计算总图形数量
                int totalEntityCount = 0;
                foreach (var combination in combinations)
                {
                    var matchingEntities = GraphicsOperationHelper.FilterEntitiesByColor(
                        entities, combination.FrameColor, combination.SeparationColor);
                    totalEntityCount += matchingEntities.Count;
                }

                editor.WriteMessage($"使用优化的复制粘贴模式处理 {totalEntityCount} 个图形...\n");

                // 统一使用优化的复制粘贴处理
                return ProcessWithOptimizedCopyPaste(entities, combinations, unitLength, editor, transaction, database);
            }
            catch (System.Exception ex)
            {
                return SeparationResult.CreateFailure($"分色处理过程中发生错误: {ex.Message}");
            }
        }



        /// <summary>
        /// 使用优化的复制粘贴方式处理分色
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="combinations">颜色组合列表</param>
        /// <param name="unitLength">单位长度</param>
        /// <param name="editor">编辑器</param>
        /// <param name="transaction">外层事务对象</param>
        /// <param name="database">数据库对象</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessWithOptimizedCopyPaste(List<Entity> entities,
            List<ColorCombination> combinations, double unitLength, Editor editor,
            Transaction transaction, Database database)
        {
            int processedCount = 0;
            int totalEntitiesProcessed = 0;

            try
            {
                foreach (var combination in combinations)
                {
                    try
                    {
                        // 筛选匹配颜色组合的图形
                        var matchingEntities = GraphicsOperationHelper.FilterEntitiesByColor(
                            entities, combination.FrameColor, combination.SeparationColor);

                        if (matchingEntities.Count == 0)
                        {
                            UserInteractionHelper.ShowWarning(editor,
                                $"未找到匹配颜色组合的图形: {combination.GetDescription()}");
                            continue;
                        }

                        // 计算Y轴偏移量（竖向分色，向下偏移）
                        double yOffset = -combination.Order * unitLength;
                        var displacement = new Vector3d(0, yOffset, 0);

                        // 显示处理信息
                        UserInteractionHelper.ShowCombinationProcessing(editor, combination,
                            matchingEntities.Count, yOffset);

                        // 简化处理：直接复制，不分批（模拟AutoLISP的COPY命令）
                        bool copySuccess = ProcessEntitiesDirectly(matchingEntities, displacement, editor);

                        if (copySuccess)
                        {
                            processedCount++;
                            totalEntitiesProcessed += matchingEntities.Count;

                            UserInteractionHelper.ShowProgress(editor,
                                $"成功处理: {combination.GetDescription()}",
                                processedCount, combinations.Count);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        UserInteractionHelper.ShowError(editor,
                            $"处理 {combination.GetDescription()} 时发生异常: {ex.Message}");
                    }
                }

                // 创建结果对象
                if (processedCount > 0)
                {
                    return SeparationResult.CreateSuccess(processedCount, totalEntitiesProcessed);
                }
                else
                {
                    return SeparationResult.CreateFailure("没有成功处理任何颜色组合");
                }
            }
            catch (System.Exception ex)
            {
                return SeparationResult.CreateFailure($"复制粘贴处理过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接处理实体（简化版本，模拟AutoLISP的COPY命令）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessEntitiesDirectly(List<Entity> entities, Vector3d displacement, Editor editor)
        {
            try
            {
                if (entities == null || entities.Count == 0)
                {
                    editor.WriteMessage("没有实体需要处理\n");
                    return true;
                }

                editor.WriteMessage($"直接复制 {entities.Count} 个实体...\n");

                // 简单直接的复制操作（模拟AutoLISP的COPY命令）
                var entityIds = GraphicsOperationHelper.CreateObjectIdCollection(entities);

                if (entityIds.Count == 0)
                {
                    editor.WriteMessage("没有有效的实体ID\n");
                    return false;
                }

                // 直接复制，不分批，不重试
                if (GraphicsOperationHelper.CopyAndMoveEntities(entityIds, displacement, out string errorMessage))
                {
                    editor.WriteMessage($"成功复制 {entityIds.Count} 个实体\n");
                    return true;
                }
                else
                {
                    editor.WriteMessage($"复制失败: {errorMessage}\n");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"直接复制发生错误: {ex.Message}\n");
                return false;
            }
        }





        /// <summary>
        /// 传统的分批处理方法（简化为直接处理）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessEntitiesInBatches(List<Entity> entities, Vector3d displacement, Editor editor)
        {
            // 简化为直接处理
            return ProcessEntitiesDirectly(entities, displacement, editor);
        }

        #endregion
    }
}
