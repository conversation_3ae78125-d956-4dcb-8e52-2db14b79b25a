using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using CADFileSaver.Core;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 分色功能管理器
    /// 负责协调整个竖向分色功能的执行流程
    /// </summary>
    public class ColorSeparationManager
    {


        #region 私有字段

        /// <summary>
        /// 外框颜色下拉框引用
        /// </summary>
        private readonly ComboBox _cmbFrameColor;

        /// <summary>
        /// 分色项目复选框引用
        /// </summary>
        private readonly CheckBox[] _colorCheckBoxes;

        /// <summary>
        /// 默认批处理大小（可根据实体类型动态调整）
        /// </summary>
        private const int DEFAULT_BATCH_SIZE = 200;

        /// <summary>
        /// 最大批处理大小
        /// </summary>
        private const int MAX_BATCH_SIZE = 1000;

        /// <summary>
        /// 最小批处理大小
        /// </summary>
        private const int MIN_BATCH_SIZE = 10;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cmbFrameColor">外框颜色下拉框</param>
        /// <param name="chkRed">红色复选框</param>
        /// <param name="chkBlue">蓝色复选框</param>
        /// <param name="chkGreen">绿色复选框</param>
        /// <param name="chkYellow">黄色复选框</param>
        /// <param name="chkCyan">青色复选框</param>
        /// <param name="chkMagenta">洋红复选框</param>
        public ColorSeparationManager(ComboBox cmbFrameColor, CheckBox chkRed, CheckBox chkBlue, 
            CheckBox chkGreen, CheckBox chkYellow, CheckBox chkCyan, CheckBox chkMagenta)
        {
            _cmbFrameColor = cmbFrameColor ?? throw new ArgumentNullException(nameof(cmbFrameColor));
            
            _colorCheckBoxes = new CheckBox[]
            {
                chkRed ?? throw new ArgumentNullException(nameof(chkRed)),
                chkBlue ?? throw new ArgumentNullException(nameof(chkBlue)),
                chkGreen ?? throw new ArgumentNullException(nameof(chkGreen)),
                chkYellow ?? throw new ArgumentNullException(nameof(chkYellow)),
                chkCyan ?? throw new ArgumentNullException(nameof(chkCyan)),
                chkMagenta ?? throw new ArgumentNullException(nameof(chkMagenta))
            };
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行竖向分色操作
        /// </summary>
        /// <returns>分色操作结果</returns>
        public SeparationResult ExecuteVerticalColorSeparation()
        {
            try
            {
                // 第一阶段：提取配置信息
                var config = ExtractConfigurationFromUI();
                if (!config.IsValid(out string validationMessage))
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.InvalidColorSelection,
                        validationMessage);
                }

                // 第二阶段：用户交互
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc.Editor;

                UserInteractionHelper.ShowOperationStart(editor, config);

                // 获取用户选择的图形
                var selectionSet = UserInteractionHelper.GetUserSelection(editor, out var status);
                
                if (status == PromptStatus.Cancel)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.UserCancelled,
                        "用户取消操作");
                }

                if (status != PromptStatus.OK || selectionSet == null)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "未选择任何图形实体");
                }

                // 第三阶段：使用全新的竖向分色处理器
                var separator = new VerticalColorSeparator(editor, doc.Database);
                var result = separator.Execute(selectionSet, config);

                // 显示结果
                UserInteractionHelper.ShowResult(editor, result);

                return result;
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"执行竖向分色时发生未预期的错误: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从UI控件提取分色配置信息
        /// </summary>
        /// <returns>分色配置对象</returns>
        private ColorSeparationConfig ExtractConfigurationFromUI()
        {
            var config = new ColorSeparationConfig();

            try
            {
                // 定义有效的颜色名称
                var validColors = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                // 提取外框颜色 - 修复：确保获取的是实际颜色值
                var selectedFrameColor = _cmbFrameColor.SelectedItem?.ToString();

                if (!string.IsNullOrEmpty(selectedFrameColor) && validColors.Contains(selectedFrameColor))
                {
                    config.FrameColor = selectedFrameColor;
                }
                else
                {
                    // 如果选择的不是有效颜色，使用默认值
                    config.FrameColor = "青色";

                    // 记录警告信息
                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    doc?.Editor.WriteMessage($"\n⚠ 警告: 外框颜色选择无效('{selectedFrameColor}')，使用默认颜色'青色'\n");
                }

                // 按固定顺序提取选中的分色项目
                var colorNames = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                for (int i = 0; i < _colorCheckBoxes.Length && i < colorNames.Length; i++)
                {
                    if (_colorCheckBoxes[i].Checked)
                    {
                        config.SeparationColors.Add(colorNames[i]);
                    }
                }

                return config;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"提取UI配置时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成颜色组合列表
        /// </summary>
        /// <param name="config">分色配置</param>
        /// <returns>颜色组合列表</returns>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            try
            {
                for (int i = 0; i < config.SeparationColors.Count; i++)
                {
                    var combination = new ColorCombination
                    {
                        FrameColor = config.FrameColor,
                        SeparationColor = config.SeparationColors[i],
                        FrameColorIndex = GetColorIndex(config.FrameColor),
                        SeparationColorIndex = GetColorIndex(config.SeparationColors[i]),
                        Order = i + 1
                    };
                    combinations.Add(combination);
                }

                return combinations;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"生成颜色组合时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取颜色索引
        /// </summary>
        /// <param name="colorName">颜色名称</param>
        /// <returns>颜色索引</returns>
        private short GetColorIndex(string colorName)
        {
            var colorMapping = new Dictionary<string, short>
            {
                { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
            };

            return colorMapping.ContainsKey(colorName) ? colorMapping[colorName] : (short)7;
        }















        /// <summary>
        /// 传统的分批处理方法（保留用于向后兼容，但已不使用）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessEntitiesInBatches(List<Entity> entities, Vector3d displacement, Editor editor)
        {
            // 这个方法已不再使用，新架构使用ExecuteAllCopyOperations
            editor.WriteMessage("警告: 使用了已废弃的分批处理方法\n");
            return false;
        }

        #endregion
    }
}
