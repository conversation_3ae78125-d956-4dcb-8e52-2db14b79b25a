using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using CADFileSaver.Core;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 分色功能管理器
    /// 负责协调整个竖向分色功能的执行流程
    /// </summary>
    public class ColorSeparationManager
    {


        #region 私有字段

        /// <summary>
        /// 外框颜色下拉框引用
        /// </summary>
        private readonly ComboBox _cmbFrameColor;

        /// <summary>
        /// 分色项目复选框引用
        /// </summary>
        private readonly CheckBox[] _colorCheckBoxes;

        /// <summary>
        /// 默认批处理大小（可根据实体类型动态调整）
        /// </summary>
        private const int DEFAULT_BATCH_SIZE = 200;

        /// <summary>
        /// 最大批处理大小
        /// </summary>
        private const int MAX_BATCH_SIZE = 1000;

        /// <summary>
        /// 最小批处理大小
        /// </summary>
        private const int MIN_BATCH_SIZE = 10;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cmbFrameColor">外框颜色下拉框</param>
        /// <param name="chkRed">红色复选框</param>
        /// <param name="chkBlue">蓝色复选框</param>
        /// <param name="chkGreen">绿色复选框</param>
        /// <param name="chkYellow">黄色复选框</param>
        /// <param name="chkCyan">青色复选框</param>
        /// <param name="chkMagenta">洋红复选框</param>
        public ColorSeparationManager(ComboBox cmbFrameColor, CheckBox chkRed, CheckBox chkBlue, 
            CheckBox chkGreen, CheckBox chkYellow, CheckBox chkCyan, CheckBox chkMagenta)
        {
            _cmbFrameColor = cmbFrameColor ?? throw new ArgumentNullException(nameof(cmbFrameColor));
            
            _colorCheckBoxes = new CheckBox[]
            {
                chkRed ?? throw new ArgumentNullException(nameof(chkRed)),
                chkBlue ?? throw new ArgumentNullException(nameof(chkBlue)),
                chkGreen ?? throw new ArgumentNullException(nameof(chkGreen)),
                chkYellow ?? throw new ArgumentNullException(nameof(chkYellow)),
                chkCyan ?? throw new ArgumentNullException(nameof(chkCyan)),
                chkMagenta ?? throw new ArgumentNullException(nameof(chkMagenta))
            };
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行竖向分色操作
        /// </summary>
        /// <returns>分色操作结果</returns>
        public SeparationResult ExecuteVerticalColorSeparation()
        {
            try
            {
                // 第一阶段：提取配置信息
                var config = ExtractConfigurationFromUI();
                if (!config.IsValid(out string validationMessage))
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.InvalidColorSelection,
                        validationMessage);
                }

                // 第二阶段：用户交互
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc.Editor;

                UserInteractionHelper.ShowOperationStart(editor, config);

                // 获取用户选择的图形
                var selectionSet = UserInteractionHelper.GetUserSelection(editor, out var status);
                
                if (status == PromptStatus.Cancel)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.UserCancelled,
                        "用户取消操作");
                }

                if (status != PromptStatus.OK || selectionSet == null)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "未选择任何图形实体");
                }

                // 第三阶段：处理选择的图形
                return ProcessSelectedEntities(selectionSet, config, editor);
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"执行竖向分色时发生未预期的错误: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从UI控件提取分色配置信息
        /// </summary>
        /// <returns>分色配置对象</returns>
        private ColorSeparationConfig ExtractConfigurationFromUI()
        {
            var config = new ColorSeparationConfig();

            try
            {
                // 定义有效的颜色名称
                var validColors = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                // 提取外框颜色 - 修复：确保获取的是实际颜色值
                var selectedFrameColor = _cmbFrameColor.SelectedItem?.ToString();

                if (!string.IsNullOrEmpty(selectedFrameColor) && validColors.Contains(selectedFrameColor))
                {
                    config.FrameColor = selectedFrameColor;
                }
                else
                {
                    // 如果选择的不是有效颜色，使用默认值
                    config.FrameColor = "青色";

                    // 记录警告信息
                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    doc?.Editor.WriteMessage($"\n⚠ 警告: 外框颜色选择无效('{selectedFrameColor}')，使用默认颜色'青色'\n");
                }

                // 按固定顺序提取选中的分色项目
                var colorNames = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                for (int i = 0; i < _colorCheckBoxes.Length && i < colorNames.Length; i++)
                {
                    if (_colorCheckBoxes[i].Checked)
                    {
                        config.SeparationColors.Add(colorNames[i]);
                    }
                }

                return config;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"提取UI配置时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成颜色组合列表
        /// </summary>
        /// <param name="config">分色配置</param>
        /// <returns>颜色组合列表</returns>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            try
            {
                for (int i = 0; i < config.SeparationColors.Count; i++)
                {
                    var combination = new ColorCombination
                    {
                        FrameColor = config.FrameColor,
                        SeparationColor = config.SeparationColors[i],
                        FrameColorIndex = GetColorIndex(config.FrameColor),
                        SeparationColorIndex = GetColorIndex(config.SeparationColors[i]),
                        Order = i + 1
                    };
                    combinations.Add(combination);
                }

                return combinations;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"生成颜色组合时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取颜色索引
        /// </summary>
        /// <param name="colorName">颜色名称</param>
        /// <returns>颜色索引</returns>
        private short GetColorIndex(string colorName)
        {
            var colorMapping = new Dictionary<string, short>
            {
                { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
            };

            return colorMapping.ContainsKey(colorName) ? colorMapping[colorName] : (short)7;
        }

        /// <summary>
        /// 处理选择的实体（统一事务管理）
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="config">分色配置</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessSelectedEntities(SelectionSet selectionSet,
            ColorSeparationConfig config, Editor editor)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;

                // 统一事务管理：只在最外层开启一个事务
                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // 提取实体
                        var entities = UserInteractionHelper.GetEntitiesFromSelection(selectionSet, transaction);

                        if (!UserInteractionHelper.ValidateSelectedEntities(entities, editor))
                        {
                            throw new ColorSeparationException(
                                ColorSeparationException.ErrorType.NoEntitiesSelected,
                                "选择的实体无效或为空");
                        }

                        // 计算单位长度
                        double unitLength = GraphicsOperationHelper.CalculateUnitLength(entities);
                        UserInteractionHelper.ShowDebugInfo(editor, $"计算得到的单位长度: {unitLength:F2}");

                        // 生成颜色组合
                        var combinations = GenerateColorCombinations(config);

                        // 执行分色处理（传递事务对象，避免嵌套）
                        var result = ProcessColorSeparationWithTransaction(entities, combinations, unitLength, editor, transaction, db);

                        // 提交事务
                        transaction.Commit();

                        // 显示结果
                        UserInteractionHelper.ShowResult(editor, result);

                        return result;
                    }
                    catch (System.Exception)
                    {
                        // 异常时自动回滚事务
                        transaction.Abort();
                        throw;
                    }
                }
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.DatabaseOperationFailed,
                    $"处理选择的实体时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理分色操作（统一使用优化的复制粘贴方式）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="combinations">颜色组合列表</param>
        /// <param name="unitLength">单位长度</param>
        /// <param name="editor">编辑器</param>
        /// <param name="transaction">外层事务对象</param>
        /// <param name="database">数据库对象</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessColorSeparationWithTransaction(List<Entity> entities,
            List<ColorCombination> combinations, double unitLength, Editor editor,
            Transaction transaction, Database database)
        {
            try
            {
                // 计算总图形数量
                int totalEntityCount = 0;
                foreach (var combination in combinations)
                {
                    var matchingEntities = GraphicsOperationHelper.FilterEntitiesByColor(
                        entities, combination.FrameColor, combination.SeparationColor);
                    totalEntityCount += matchingEntities.Count;
                }

                editor.WriteMessage($"使用优化的复制粘贴模式处理 {totalEntityCount} 个图形...\n");

                // 统一使用优化的复制粘贴处理
                return ProcessWithOptimizedCopyPaste(entities, combinations, unitLength, editor, transaction, database);
            }
            catch (System.Exception ex)
            {
                return SeparationResult.CreateFailure($"分色处理过程中发生错误: {ex.Message}");
            }
        }



        /// <summary>
        /// 使用优化的复制粘贴方式处理分色
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="combinations">颜色组合列表</param>
        /// <param name="unitLength">单位长度</param>
        /// <param name="editor">编辑器</param>
        /// <param name="transaction">外层事务对象</param>
        /// <param name="database">数据库对象</param>
        /// <returns>处理结果</returns>
        private SeparationResult ProcessWithOptimizedCopyPaste(List<Entity> entities,
            List<ColorCombination> combinations, double unitLength, Editor editor,
            Transaction transaction, Database database)
        {
            int processedCount = 0;
            int totalEntitiesProcessed = 0;

            try
            {
                foreach (var combination in combinations)
                {
                    try
                    {
                        // 筛选匹配颜色组合的图形
                        var matchingEntities = GraphicsOperationHelper.FilterEntitiesByColor(
                            entities, combination.FrameColor, combination.SeparationColor);

                        if (matchingEntities.Count == 0)
                        {
                            UserInteractionHelper.ShowWarning(editor,
                                $"未找到匹配颜色组合的图形: {combination.GetDescription()}");
                            continue;
                        }

                        // 计算Y轴偏移量（竖向分色，向下偏移）
                        double yOffset = -combination.Order * unitLength;
                        var displacement = new Vector3d(0, yOffset, 0);

                        // 显示处理信息
                        UserInteractionHelper.ShowCombinationProcessing(editor, combination,
                            matchingEntities.Count, yOffset);

                        // 简化处理：直接复制，不分批（模拟AutoLISP的COPY命令）
                        bool copySuccess = ProcessEntitiesDirectly(matchingEntities, displacement, editor);

                        if (copySuccess)
                        {
                            processedCount++;
                            totalEntitiesProcessed += matchingEntities.Count;

                            UserInteractionHelper.ShowProgress(editor,
                                $"成功处理: {combination.GetDescription()}",
                                processedCount, combinations.Count);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        UserInteractionHelper.ShowError(editor,
                            $"处理 {combination.GetDescription()} 时发生异常: {ex.Message}");
                    }
                }

                // 创建结果对象
                if (processedCount > 0)
                {
                    return SeparationResult.CreateSuccess(processedCount, totalEntitiesProcessed);
                }
                else
                {
                    return SeparationResult.CreateFailure("没有成功处理任何颜色组合");
                }
            }
            catch (System.Exception ex)
            {
                return SeparationResult.CreateFailure($"复制粘贴处理过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接处理实体（简化版本，模拟AutoLISP的COPY命令）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessEntitiesDirectly(List<Entity> entities, Vector3d displacement, Editor editor)
        {
            try
            {
                if (entities == null || entities.Count == 0)
                {
                    editor.WriteMessage("没有实体需要处理\n");
                    return true;
                }

                // 按实体类型分组并确定批次大小
                var entityGroups = GroupEntitiesByComplexity(entities);
                int totalBatches = 0;
                int successfulBatches = 0;

                editor.WriteMessage($"智能分批处理 {entities.Count} 个实体...\n");

                // 处理每个复杂度组
                foreach (var group in entityGroups)
                {
                    var groupEntities = group.Value;
                    int batchSize = GetOptimalBatchSize(group.Key);

                    editor.WriteMessage($"{group.Key} 类型: {groupEntities.Count} 个实体，批次大小: {batchSize}\n");

                    // 分批处理当前组
                    for (int i = 0; i < groupEntities.Count; i += batchSize)
                    {
                        var batch = groupEntities.Skip(i).Take(batchSize).ToList();
                        totalBatches++;

                        if (ProcessSingleBatchWithRetry(batch, displacement, editor, totalBatches, group.Key))
                        {
                            successfulBatches++;
                        }
                        else
                        {
                            editor.WriteMessage($"批次 {totalBatches} 处理失败，但继续处理其他批次\n");
                        }

                        // 在批次之间添加短暂延迟，避免CAD过载
                        if (i + batchSize < groupEntities.Count)
                        {
                            System.Threading.Thread.Sleep(30);
                        }
                    }
                }

                editor.WriteMessage($"智能分批处理完成: {successfulBatches}/{totalBatches} 批成功\n");
                return successfulBatches > 0;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"智能分批处理发生错误: {ex.Message}\n");
                return false;
            }
        }

        /// <summary>
        /// 按复杂度对实体进行分组
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>按复杂度分组的实体字典</returns>
        private Dictionary<string, List<Entity>> GroupEntitiesByComplexity(List<Entity> entities)
        {
            var groups = new Dictionary<string, List<Entity>>
            {
                ["简单图形"] = new List<Entity>(),
                ["中等图形"] = new List<Entity>(),
                ["复杂图形"] = new List<Entity>()
            };

            foreach (var entity in entities)
            {
                if (entity == null) continue;

                string complexity = GetEntityComplexity(entity);
                groups[complexity].Add(entity);
            }

            // 移除空组
            return groups.Where(g => g.Value.Count > 0).ToDictionary(g => g.Key, g => g.Value);
        }

        /// <summary>
        /// 获取实体的复杂度分类
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>复杂度分类</returns>
        private string GetEntityComplexity(Entity entity)
        {
            // 简单图形：直线、圆、圆弧
            if (entity is Line || entity is Circle || entity is Arc)
            {
                return "简单图形";
            }

            // 中等图形：多段线、椭圆
            if (entity is Polyline || entity is Ellipse)
            {
                return "中等图形";
            }

            // 复杂图形：样条曲线、块引用、文字等
            return "复杂图形";
        }

        /// <summary>
        /// 根据实体复杂度获取最优批次大小
        /// </summary>
        /// <param name="complexity">复杂度分类</param>
        /// <returns>最优批次大小</returns>
        private int GetOptimalBatchSize(string complexity)
        {
            switch (complexity)
            {
                case "简单图形":
                    return Math.Min(MAX_BATCH_SIZE, 800); // 简单图形可以大批次处理
                case "中等图形":
                    return Math.Min(MAX_BATCH_SIZE, 400); // 中等图形中等批次
                case "复杂图形":
                    return Math.Min(MAX_BATCH_SIZE, 100); // 复杂图形小批次处理
                default:
                    return DEFAULT_BATCH_SIZE;
            }
        }

        /// <summary>
        /// 处理单个批次（带重试机制）
        /// </summary>
        /// <param name="batch">批次实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <param name="batchNumber">批次编号</param>
        /// <param name="entityType">实体类型</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessSingleBatchWithRetry(List<Entity> batch, Vector3d displacement,
            Editor editor, int batchNumber, string entityType)
        {
            const int maxRetries = 3;
            int currentBatchSize = batch.Count;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    editor.WriteMessage($"批次 {batchNumber} ({entityType}): 处理 {currentBatchSize} 个实体 (尝试 {attempt}/{maxRetries})...\n");

                    // 创建ObjectId集合
                    var entityIds = GraphicsOperationHelper.CreateObjectIdCollection(batch);

                    if (entityIds.Count == 0)
                    {
                        editor.WriteMessage($"批次 {batchNumber}: 没有有效的实体ID\n");
                        return false;
                    }

                    // 尝试复制和移动实体
                    if (GraphicsOperationHelper.CopyAndMoveEntities(entityIds, displacement, out string errorMessage))
                    {
                        editor.WriteMessage($"批次 {batchNumber}: 成功处理 {entityIds.Count} 个实体\n");
                        return true;
                    }
                    else
                    {
                        editor.WriteMessage($"批次 {batchNumber} 尝试 {attempt} 失败: {errorMessage}\n");

                        // 如果不是最后一次尝试，考虑减小批次大小
                        if (attempt < maxRetries && batch.Count > MIN_BATCH_SIZE)
                        {
                            // 将批次分成两半重试
                            int halfSize = Math.Max(MIN_BATCH_SIZE, batch.Count / 2);
                            var firstHalf = batch.Take(halfSize).ToList();
                            var secondHalf = batch.Skip(halfSize).ToList();

                            editor.WriteMessage($"批次 {batchNumber}: 分割为两个子批次重试 ({firstHalf.Count} + {secondHalf.Count})\n");

                            bool firstSuccess = ProcessSingleBatchWithRetry(firstHalf, displacement, editor,
                                batchNumber * 10 + 1, entityType);
                            bool secondSuccess = ProcessSingleBatchWithRetry(secondHalf, displacement, editor,
                                batchNumber * 10 + 2, entityType);

                            return firstSuccess && secondSuccess;
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"批次 {batchNumber} 尝试 {attempt} 异常: {ex.Message}\n");

                    // 如果是最后一次尝试，返回失败
                    if (attempt == maxRetries)
                    {
                        return false;
                    }
                }

                // 在重试之间添加延迟
                if (attempt < maxRetries)
                {
                    System.Threading.Thread.Sleep(100 * attempt);
                }
            }

            return false;
        }

        /// <summary>
        /// 传统的分批处理方法（保留用于向后兼容）
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="displacement">位移向量</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessEntitiesInBatches(List<Entity> entities, Vector3d displacement, Editor editor)
        {
            // 使用新的智能分批处理
            return ProcessEntitiesWithIntelligentBatching(entities, displacement, editor,
                new ColorCombination { FrameColor = "默认", SeparationColor = "默认", Order = 1 });
        }

        #endregion
    }
}
