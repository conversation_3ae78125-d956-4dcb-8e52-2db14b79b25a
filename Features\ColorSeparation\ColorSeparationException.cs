using System;
using System.Windows.Forms;
using Autodesk.AutoCAD.EditorInput;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 分色功能专用异常类
    /// 提供详细的错误分类和处理机制
    /// </summary>
    public class ColorSeparationException : Exception
    {
        /// <summary>
        /// 错误类型枚举
        /// </summary>
        public enum ErrorType
        {
            /// <summary>
            /// 无效的颜色选择配置
            /// </summary>
            InvalidColorSelection,

            /// <summary>
            /// 未选择任何图形实体
            /// </summary>
            NoEntitiesSelected,

            /// <summary>
            /// 图形操作失败
            /// </summary>
            GraphicsOperationFailed,

            /// <summary>
            /// 用户取消操作
            /// </summary>
            UserCancelled,

            /// <summary>
            /// CAD数据库操作失败
            /// </summary>
            DatabaseOperationFailed,

            /// <summary>
            /// 颜色匹配失败
            /// </summary>
            ColorMatchingFailed
        }

        /// <summary>
        /// 错误类型
        /// </summary>
        public ErrorType Type { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="type">错误类型</param>
        /// <param name="message">错误消息</param>
        public ColorSeparationException(ErrorType type, string message) : base(message)
        {
            Type = type;
        }

        /// <summary>
        /// 构造函数（包含内部异常）
        /// </summary>
        /// <param name="type">错误类型</param>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public ColorSeparationException(ErrorType type, string message, Exception innerException) 
            : base(message, innerException)
        {
            Type = type;
        }

        /// <summary>
        /// 获取用户友好的错误消息
        /// </summary>
        /// <returns>用户友好的错误消息</returns>
        public string GetUserFriendlyMessage()
        {
            switch (Type)
            {
                case ErrorType.InvalidColorSelection:
                    return "请至少选择一个分色项目";

                case ErrorType.NoEntitiesSelected:
                    return "请选择要进行分色处理的图形";

                case ErrorType.UserCancelled:
                    return "操作已取消";

                case ErrorType.GraphicsOperationFailed:
                    return "图形处理失败，请检查选择的图形是否有效";

                case ErrorType.DatabaseOperationFailed:
                    return "CAD数据库操作失败，请重试";

                case ErrorType.ColorMatchingFailed:
                    return "未找到匹配指定颜色的图形";

                default:
                    return "分色操作失败";
            }
        }
    }

    /// <summary>
    /// 安全执行包装器
    /// 确保分色功能的异常不会影响其他Box功能的正常运行
    /// </summary>
    public static class SafeExecutionWrapper
    {
        /// <summary>
        /// 安全执行分色操作
        /// </summary>
        /// <param name="colorSeparationAction">分色操作委托</param>
        public static void ExecuteColorSeparation(Action colorSeparationAction)
        {
            try
            {
                // 执行分色操作
                colorSeparationAction();
            }
            catch (ColorSeparationException ex)
            {
                // 处理分色功能特定异常
                HandleColorSeparationError(ex);
            }
            catch (System.Exception ex)
            {
                // 处理通用异常
                HandleGeneralError(ex);
            }
        }

        /// <summary>
        /// 处理分色功能特定异常
        /// </summary>
        /// <param name="ex">分色异常</param>
        private static void HandleColorSeparationError(ColorSeparationException ex)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc?.Editor;

                if (editor != null)
                {
                    // 在CAD命令行显示错误信息
                    editor.WriteMessage($"\n[分色功能错误] {ex.GetUserFriendlyMessage()}\n");
                    
                    // 如果不是用户取消，显示详细错误信息
                    if (ex.Type != ColorSeparationException.ErrorType.UserCancelled)
                    {
                        editor.WriteMessage($"详细信息: {ex.Message}\n");
                    }
                }
                else
                {
                    // 如果无法获取编辑器，使用消息框显示
                    MessageBox.Show(ex.GetUserFriendlyMessage(), "分色功能提示", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch
            {
                // 如果错误处理本身失败，使用最基本的消息框
                MessageBox.Show("分色功能执行失败", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理通用异常
        /// </summary>
        /// <param name="ex">通用异常</param>
        private static void HandleGeneralError(System.Exception ex)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc?.Editor;

                if (editor != null)
                {
                    editor.WriteMessage($"\n[分色功能异常] 操作失败: {ex.Message}\n");
                    editor.WriteMessage("请检查图形选择和颜色配置后重试\n");
                }
                else
                {
                    MessageBox.Show($"分色功能执行异常: {ex.Message}", "错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                // 最后的异常处理保障
                MessageBox.Show("分色功能执行异常", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 安全执行带返回值的分色操作
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="colorSeparationFunc">分色操作函数</param>
        /// <param name="defaultValue">异常时的默认返回值</param>
        /// <returns>操作结果或默认值</returns>
        public static T ExecuteColorSeparation<T>(Func<T> colorSeparationFunc, T defaultValue = default(T))
        {
            try
            {
                return colorSeparationFunc();
            }
            catch (ColorSeparationException ex)
            {
                HandleColorSeparationError(ex);
                return defaultValue;
            }
            catch (System.Exception ex)
            {
                HandleGeneralError(ex);
                return defaultValue;
            }
        }
    }
}
