using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using CADFileSaver.Core;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 全新的竖向分色处理器 - 完全避免eLockViolation
    /// </summary>
    public class VerticalColorSeparator
    {
        private readonly Editor _editor;
        private readonly Database _database;

        public VerticalColorSeparator(Editor editor, Database database)
        {
            _editor = editor ?? throw new ArgumentNullException(nameof(editor));
            _database = database ?? throw new ArgumentNullException(nameof(database));
        }

        /// <summary>
        /// 执行竖向分色操作（全新实现，单事务模式）
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="config">分色配置</param>
        /// <returns>处理结果</returns>
        public SeparationResult Execute(SelectionSet selectionSet, ColorSeparationConfig config)
        {
            if (selectionSet == null || selectionSet.Count == 0)
            {
                return SeparationResult.CreateFailure("没有选择任何图形");
            }

            if (config == null || config.SeparationColors == null || config.SeparationColors.Count == 0)
            {
                return SeparationResult.CreateFailure("分色配置无效");
            }

            _editor.WriteMessage("开始执行竖向分色操作（测试版本：仅处理分色项目颜色）...\n");

            try
            {
                // 单一事务处理所有操作
                using (var transaction = _database.TransactionManager.StartTransaction())
                {
                    // 步骤1：收集所有ObjectId
                    var objectIds = CollectObjectIds(selectionSet);
                    _editor.WriteMessage($"收集到 {objectIds.Count} 个对象\n");

                    if (objectIds.Count == 0)
                    {
                        return SeparationResult.CreateFailure("没有有效的对象");
                    }

                    // 步骤2：计算单位长度
                    double unitLength = CalculateUnitLength(objectIds, transaction);
                    _editor.WriteMessage($"计算得到单位长度: {unitLength:F2}\n");

                    // 步骤3：生成颜色组合
                    var combinations = GenerateColorCombinations(config);
                    _editor.WriteMessage($"生成 {combinations.Count} 个颜色组合\n");

                    // 步骤4：处理每个颜色组合
                    var result = ProcessColorCombinations(objectIds, combinations, unitLength, transaction);

                    // 步骤5：提交事务
                    transaction.Commit();
                    _editor.WriteMessage("竖向分色操作完成（测试版本）\n");

                    return result;
                }
            }
            catch (Exception ex)
            {
                _editor.WriteMessage($"竖向分色操作失败: {ex.Message}\n");
                return SeparationResult.CreateFailure($"操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 收集ObjectId列表（无事务操作）
        /// </summary>
        private List<ObjectId> CollectObjectIds(SelectionSet selectionSet)
        {
            var objectIds = new List<ObjectId>();

            foreach (SelectedObject selectedObj in selectionSet)
            {
                if (selectedObj?.ObjectId != null && 
                    !selectedObj.ObjectId.IsNull && 
                    !selectedObj.ObjectId.IsErased && 
                    selectedObj.ObjectId.IsValid)
                {
                    objectIds.Add(selectedObj.ObjectId);
                }
            }

            return objectIds;
        }

        /// <summary>
        /// 计算单位长度（在指定事务中）
        /// </summary>
        private double CalculateUnitLength(List<ObjectId> objectIds, Transaction transaction)
        {
            double maxLength = 0;

            foreach (var objectId in objectIds)
            {
                try
                {
                    var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                    if (entity != null && !entity.IsDisposed)
                    {
                        double length = GetEntityLength(entity);
                        if (length > maxLength)
                        {
                            maxLength = length;
                        }
                    }
                }
                catch
                {
                    // 跳过有问题的对象
                    continue;
                }
            }

            return maxLength > 0 ? maxLength + 300 : 1000; // 默认值1000
        }

        /// <summary>
        /// 获取实体长度（简化版本）
        /// </summary>
        private double GetEntityLength(Entity entity)
        {
            try
            {
                switch (entity)
                {
                    case Line line:
                        return line.Length;
                    case Polyline polyline:
                        return polyline.Length;
                    case Arc arc:
                        return arc.Length;
                    case Circle circle:
                        return circle.Circumference;
                    default:
                        // 使用边界框对角线长度
                        var extents = entity.GeometricExtents;
                        return extents.MinPoint.DistanceTo(extents.MaxPoint);
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 生成颜色组合
        /// </summary>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            for (int i = 0; i < config.SeparationColors.Count; i++)
            {
                combinations.Add(new ColorCombination
                {
                    FrameColor = config.FrameColor,
                    SeparationColor = config.SeparationColors[i],
                    Order = i + 1
                });
            }

            return combinations;
        }

        /// <summary>
        /// 处理所有颜色组合（一次性获取Entity对象，避免重复访问）
        /// </summary>
        private SeparationResult ProcessColorCombinations(List<ObjectId> objectIds,
            List<ColorCombination> combinations, double unitLength, Transaction transaction)
        {
            int processedCount = 0;
            int totalEntitiesProcessed = 0;

            try
            {
                // 一次性获取所有Entity对象，避免重复访问同一ObjectId
                _editor.WriteMessage($"一次性获取 {objectIds.Count} 个实体对象...\n");
                var entityMap = new Dictionary<ObjectId, Entity>();

                foreach (var objectId in objectIds)
                {
                    try
                    {
                        var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                        if (entity != null && !entity.IsDisposed)
                        {
                            entityMap[objectId] = entity;
                        }
                    }
                    catch
                    {
                        // 跳过有问题的对象
                        continue;
                    }
                }

                _editor.WriteMessage($"成功获取 {entityMap.Count} 个有效实体对象\n");

                // 处理每个颜色组合
                foreach (var combination in combinations)
                {
                    try
                    {
                        _editor.WriteMessage($"处理分色项目颜色: {combination.SeparationColor}（测试版本：忽略外框颜色 {combination.FrameColor}）\n");

                        // 使用已获取的Entity对象进行颜色筛选
                        var matchingEntities = FilterEntitiesByColor(entityMap, combination);

                        if (matchingEntities.Count == 0)
                        {
                            _editor.WriteMessage($"未找到匹配的图形\n");
                            continue;
                        }

                        _editor.WriteMessage($"找到 {matchingEntities.Count} 个匹配图形\n");

                        // 计算偏移量
                        double yOffset = -combination.Order * unitLength;
                        var displacement = new Vector3d(0, yOffset, 0);

                        // 使用已获取的Entity对象进行复制
                        bool success = CopyEntities(matchingEntities, displacement, transaction);

                        if (success)
                        {
                            processedCount++;
                            totalEntitiesProcessed += matchingEntities.Count;
                            _editor.WriteMessage($"成功复制 {matchingEntities.Count} 个对象\n");
                        }
                        else
                        {
                            _editor.WriteMessage($"复制失败\n");
                        }
                    }
                    catch (Exception ex)
                    {
                        _editor.WriteMessage($"处理颜色组合时出错: {ex.Message}\n");
                    }
                }

                return processedCount > 0 ?
                    SeparationResult.CreateSuccess(processedCount, totalEntitiesProcessed) :
                    SeparationResult.CreateFailure("没有成功处理任何颜色组合");
            }
            finally
            {
                // 确保在方法结束时释放所有Entity对象引用
                // 注意：不需要手动Dispose，事务提交时会自动处理
                _editor.WriteMessage("释放实体对象引用...\n");
            }
        }

        /// <summary>
        /// 根据颜色筛选Entity对象（测试版本：只匹配分色项目颜色，不匹配外框颜色）
        /// </summary>
        private List<Entity> FilterEntitiesByColor(Dictionary<ObjectId, Entity> entityMap,
            ColorCombination combination)
        {
            var matchingEntities = new List<Entity>();

            foreach (var kvp in entityMap)
            {
                try
                {
                    var entity = kvp.Value;
                    if (entity != null && !entity.IsDisposed)
                    {
                        // 测试版本：只匹配分色项目的颜色，不匹配外框颜色
                        // 这样可以简化逻辑，更容易验证eLockViolation是否解决
                        if (IsColorMatch(entity, combination.SeparationColor))
                        {
                            matchingEntities.Add(entity);
                        }
                    }
                }
                catch
                {
                    // 跳过有问题的对象
                    continue;
                }
            }

            return matchingEntities;
        }

        /// <summary>
        /// 根据颜色筛选ObjectId（保留用于向后兼容，但已不推荐使用）
        /// </summary>
        private List<ObjectId> FilterObjectIdsByColor(List<ObjectId> objectIds,
            ColorCombination combination, Transaction transaction)
        {
            var matchingIds = new List<ObjectId>();

            foreach (var objectId in objectIds)
            {
                try
                {
                    var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                    if (entity != null && !entity.IsDisposed)
                    {
                        if (IsColorMatch(entity, combination.FrameColor) ||
                            IsColorMatch(entity, combination.SeparationColor))
                        {
                            matchingIds.Add(objectId);
                        }
                    }
                }
                catch
                {
                    // 跳过有问题的对象
                    continue;
                }
            }

            return matchingIds;
        }

        /// <summary>
        /// 检查颜色匹配（优化版本，确保安全的颜色访问）
        /// </summary>
        private bool IsColorMatch(Entity entity, string colorName)
        {
            try
            {
                // 验证实体有效性
                if (entity == null || entity.IsDisposed)
                    return false;

                var colorMapping = new Dictionary<string, short>
                {
                    { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                    { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
                };

                if (!colorMapping.ContainsKey(colorName))
                    return false;

                short targetColorIndex = colorMapping[colorName];

                // 安全地获取实体颜色索引
                short entityColorIndex;
                try
                {
                    entityColorIndex = entity.Color.ColorIndex;
                }
                catch
                {
                    // 如果无法获取颜色索引，返回false
                    return false;
                }

                return entityColorIndex == targetColorIndex;
            }
            catch (Exception ex)
            {
                // 记录调试信息但不影响程序运行
                System.Diagnostics.Debug.WriteLine($"IsColorMatch异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 复制实体对象（使用已获取的Entity对象，避免重复访问）
        /// </summary>
        private bool CopyEntities(List<Entity> entities, Vector3d displacement, Transaction transaction)
        {
            try
            {
                // 获取模型空间
                var blockTable = transaction.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;
                var modelSpace = transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                int copiedCount = 0;

                foreach (var originalEntity in entities)
                {
                    try
                    {
                        if (originalEntity != null && !originalEntity.IsDisposed)
                        {
                            // 创建副本（使用已获取的Entity对象，避免重复访问ObjectId）
                            var copiedEntity = originalEntity.Clone() as Entity;
                            if (copiedEntity != null)
                            {
                                // 应用位移
                                copiedEntity.TransformBy(Matrix3d.Displacement(displacement));

                                // 添加到模型空间
                                modelSpace.AppendEntity(copiedEntity);
                                transaction.AddNewlyCreatedDBObject(copiedEntity, true);

                                copiedCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录具体错误但继续处理其他对象
                        _editor.WriteMessage($"复制单个实体时出错: {ex.Message}\n");
                        continue;
                    }
                }

                return copiedCount > 0;
            }
            catch (Exception ex)
            {
                _editor.WriteMessage($"复制实体时发生错误: {ex.Message}\n");
                return false;
            }
        }

        /// <summary>
        /// 复制对象（保留用于向后兼容，但已不推荐使用）
        /// </summary>
        private bool CopyObjects(List<ObjectId> objectIds, Vector3d displacement, Transaction transaction)
        {
            try
            {
                // 获取模型空间
                var blockTable = transaction.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;
                var modelSpace = transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                int copiedCount = 0;

                foreach (var objectId in objectIds)
                {
                    try
                    {
                        var originalEntity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                        if (originalEntity != null && !originalEntity.IsDisposed)
                        {
                            // 创建副本
                            var copiedEntity = originalEntity.Clone() as Entity;
                            if (copiedEntity != null)
                            {
                                // 应用位移
                                copiedEntity.TransformBy(Matrix3d.Displacement(displacement));

                                // 添加到模型空间
                                modelSpace.AppendEntity(copiedEntity);
                                transaction.AddNewlyCreatedDBObject(copiedEntity, true);

                                copiedCount++;
                            }
                        }
                    }
                    catch
                    {
                        // 跳过有问题的对象
                        continue;
                    }
                }

                return copiedCount > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
