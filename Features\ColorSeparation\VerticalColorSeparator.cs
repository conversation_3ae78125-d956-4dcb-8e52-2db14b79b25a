using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using CADFileSaver.Core;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 全新的竖向分色处理器 - 完全避免eLockViolation
    /// </summary>
    public class VerticalColorSeparator
    {
        private readonly Editor _editor;
        private readonly Database _database;

        public VerticalColorSeparator(Editor editor, Database database)
        {
            _editor = editor ?? throw new ArgumentNullException(nameof(editor));
            _database = database ?? throw new ArgumentNullException(nameof(database));
        }

        /// <summary>
        /// 执行竖向分色操作（全新实现，单事务模式）
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="config">分色配置</param>
        /// <returns>处理结果</returns>
        public SeparationResult Execute(SelectionSet selectionSet, ColorSeparationConfig config)
        {
            if (selectionSet == null || selectionSet.Count == 0)
            {
                return SeparationResult.CreateFailure("没有选择任何图形");
            }

            if (config == null || config.SeparationColors == null || config.SeparationColors.Count == 0)
            {
                return SeparationResult.CreateFailure("分色配置无效");
            }

            _editor.WriteMessage("开始执行竖向分色操作...\n");

            try
            {
                // 单一事务处理所有操作
                using (var transaction = _database.TransactionManager.StartTransaction())
                {
                    // 步骤1：收集所有ObjectId
                    var objectIds = CollectObjectIds(selectionSet);
                    _editor.WriteMessage($"收集到 {objectIds.Count} 个对象\n");

                    if (objectIds.Count == 0)
                    {
                        return SeparationResult.CreateFailure("没有有效的对象");
                    }

                    // 步骤2：计算单位长度
                    double unitLength = CalculateUnitLength(objectIds, transaction);
                    _editor.WriteMessage($"计算得到单位长度: {unitLength:F2}\n");

                    // 步骤3：生成颜色组合
                    var combinations = GenerateColorCombinations(config);
                    _editor.WriteMessage($"生成 {combinations.Count} 个颜色组合\n");

                    // 步骤4：处理每个颜色组合
                    var result = ProcessColorCombinations(objectIds, combinations, unitLength, transaction);

                    // 步骤5：提交事务
                    transaction.Commit();
                    _editor.WriteMessage("竖向分色操作完成\n");

                    return result;
                }
            }
            catch (Exception ex)
            {
                _editor.WriteMessage($"竖向分色操作失败: {ex.Message}\n");
                return SeparationResult.CreateFailure($"操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 收集ObjectId列表（无事务操作）
        /// </summary>
        private List<ObjectId> CollectObjectIds(SelectionSet selectionSet)
        {
            var objectIds = new List<ObjectId>();

            foreach (SelectedObject selectedObj in selectionSet)
            {
                if (selectedObj?.ObjectId != null && 
                    !selectedObj.ObjectId.IsNull && 
                    !selectedObj.ObjectId.IsErased && 
                    selectedObj.ObjectId.IsValid)
                {
                    objectIds.Add(selectedObj.ObjectId);
                }
            }

            return objectIds;
        }

        /// <summary>
        /// 计算单位长度（在指定事务中）
        /// </summary>
        private double CalculateUnitLength(List<ObjectId> objectIds, Transaction transaction)
        {
            double maxLength = 0;

            foreach (var objectId in objectIds)
            {
                try
                {
                    var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                    if (entity != null && !entity.IsDisposed)
                    {
                        double length = GetEntityLength(entity);
                        if (length > maxLength)
                        {
                            maxLength = length;
                        }
                    }
                }
                catch
                {
                    // 跳过有问题的对象
                    continue;
                }
            }

            return maxLength > 0 ? maxLength + 300 : 1000; // 默认值1000
        }

        /// <summary>
        /// 获取实体长度（简化版本）
        /// </summary>
        private double GetEntityLength(Entity entity)
        {
            try
            {
                switch (entity)
                {
                    case Line line:
                        return line.Length;
                    case Polyline polyline:
                        return polyline.Length;
                    case Arc arc:
                        return arc.Length;
                    case Circle circle:
                        return circle.Circumference;
                    default:
                        // 使用边界框对角线长度
                        var extents = entity.GeometricExtents;
                        return extents.MinPoint.DistanceTo(extents.MaxPoint);
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 生成颜色组合
        /// </summary>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            for (int i = 0; i < config.SeparationColors.Count; i++)
            {
                combinations.Add(new ColorCombination
                {
                    FrameColor = config.FrameColor,
                    SeparationColor = config.SeparationColors[i],
                    Order = i + 1
                });
            }

            return combinations;
        }

        /// <summary>
        /// 处理所有颜色组合（在指定事务中）
        /// </summary>
        private SeparationResult ProcessColorCombinations(List<ObjectId> objectIds, 
            List<ColorCombination> combinations, double unitLength, Transaction transaction)
        {
            int processedCount = 0;
            int totalEntitiesProcessed = 0;

            foreach (var combination in combinations)
            {
                try
                {
                    _editor.WriteMessage($"处理颜色组合: {combination.FrameColor} + {combination.SeparationColor}\n");

                    // 筛选匹配的ObjectId
                    var matchingIds = FilterObjectIdsByColor(objectIds, combination, transaction);
                    
                    if (matchingIds.Count == 0)
                    {
                        _editor.WriteMessage($"未找到匹配的图形\n");
                        continue;
                    }

                    _editor.WriteMessage($"找到 {matchingIds.Count} 个匹配图形\n");

                    // 计算偏移量
                    double yOffset = -combination.Order * unitLength;
                    var displacement = new Vector3d(0, yOffset, 0);

                    // 复制对象
                    bool success = CopyObjects(matchingIds, displacement, transaction);

                    if (success)
                    {
                        processedCount++;
                        totalEntitiesProcessed += matchingIds.Count;
                        _editor.WriteMessage($"成功复制 {matchingIds.Count} 个对象\n");
                    }
                    else
                    {
                        _editor.WriteMessage($"复制失败\n");
                    }
                }
                catch (Exception ex)
                {
                    _editor.WriteMessage($"处理颜色组合时出错: {ex.Message}\n");
                }
            }

            return processedCount > 0 ? 
                SeparationResult.CreateSuccess(processedCount, totalEntitiesProcessed) :
                SeparationResult.CreateFailure("没有成功处理任何颜色组合");
        }

        /// <summary>
        /// 根据颜色筛选ObjectId（在指定事务中）
        /// </summary>
        private List<ObjectId> FilterObjectIdsByColor(List<ObjectId> objectIds, 
            ColorCombination combination, Transaction transaction)
        {
            var matchingIds = new List<ObjectId>();

            foreach (var objectId in objectIds)
            {
                try
                {
                    var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                    if (entity != null && !entity.IsDisposed)
                    {
                        if (IsColorMatch(entity, combination.FrameColor) || 
                            IsColorMatch(entity, combination.SeparationColor))
                        {
                            matchingIds.Add(objectId);
                        }
                    }
                }
                catch
                {
                    // 跳过有问题的对象
                    continue;
                }
            }

            return matchingIds;
        }

        /// <summary>
        /// 检查颜色匹配（最简化版本）
        /// </summary>
        private bool IsColorMatch(Entity entity, string colorName)
        {
            try
            {
                var colorMapping = new Dictionary<string, short>
                {
                    { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                    { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
                };

                if (!colorMapping.ContainsKey(colorName))
                    return false;

                short targetColorIndex = colorMapping[colorName];
                short entityColorIndex = entity.Color.ColorIndex;

                return entityColorIndex == targetColorIndex;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 复制对象（在指定事务中）
        /// </summary>
        private bool CopyObjects(List<ObjectId> objectIds, Vector3d displacement, Transaction transaction)
        {
            try
            {
                // 获取模型空间
                var blockTable = transaction.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;
                var modelSpace = transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                int copiedCount = 0;

                foreach (var objectId in objectIds)
                {
                    try
                    {
                        var originalEntity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                        if (originalEntity != null && !originalEntity.IsDisposed)
                        {
                            // 创建副本
                            var copiedEntity = originalEntity.Clone() as Entity;
                            if (copiedEntity != null)
                            {
                                // 应用位移
                                copiedEntity.TransformBy(Matrix3d.Displacement(displacement));

                                // 添加到模型空间
                                modelSpace.AppendEntity(copiedEntity);
                                transaction.AddNewlyCreatedDBObject(copiedEntity, true);

                                copiedCount++;
                            }
                        }
                    }
                    catch
                    {
                        // 跳过有问题的对象
                        continue;
                    }
                }

                return copiedCount > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
