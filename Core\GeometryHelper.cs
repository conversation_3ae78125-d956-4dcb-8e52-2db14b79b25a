using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Colors;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 几何计算辅助类
    /// </summary>
    public static class GeometryHelper
    {
        /// <summary>
        /// 颜色名称到AutoCAD颜色索引的映射
        /// </summary>
        private static readonly Dictionary<string, short> ColorMapping = new Dictionary<string, short>
        {
            { "红色", 1 },   // Red
            { "黄色", 2 },   // Yellow  
            { "绿色", 3 },   // Green
            { "青色", 4 },   // <PERSON><PERSON>
            { "蓝色", 5 },   // Blue
            { "洋红", 6 }    // Magenta
        };

        /// <summary>
        /// 获取实体的长度
        /// </summary>
        public static double GetEntityLength(Entity entity)
        {
            try
            {
                switch (entity)
                {
                    case Line line:
                        return line.Length;
                    
                    case Polyline polyline:
                        return polyline.Length;
                    
                    case Polyline2d polyline2d:
                        return GetPolyline2dLength(polyline2d);
                    
                    case Arc arc:
                        return arc.Length;
                    
                    case Circle circle:
                        return circle.Circumference;
                    
                    case Ellipse ellipse:
                        return GetEllipseLength(ellipse);
                    
                    case Spline spline:
                        return GetSplineLength(spline);
                    
                    default:
                        // 对于其他类型的实体，使用边界框对角线长度
                        return GetBoundingBoxDiagonal(entity);
                }
            }
            catch (Exception)
            {
                // 如果计算失败，返回边界框对角线长度
                return GetBoundingBoxDiagonal(entity);
            }
        }

        /// <summary>
        /// 获取2D多段线长度
        /// </summary>
        private static double GetPolyline2dLength(Polyline2d polyline2d)
        {
            // 对于2D多段线，暂时使用边界框对角线长度
            // 这里可以在后续版本中实现更精确的计算
            return GetBoundingBoxDiagonal(polyline2d);
        }

        /// <summary>
        /// 获取椭圆长度（周长）
        /// </summary>
        private static double GetEllipseLength(Ellipse ellipse)
        {
            // 椭圆周长的近似计算
            double a = ellipse.MajorRadius;
            double b = ellipse.MinorRadius;
            
            if (Math.Abs(a - b) < 1e-10) // 圆形
            {
                return 2 * Math.PI * a;
            }
            
            // 使用拉马努金近似公式
            double h = Math.Pow((a - b) / (a + b), 2);
            return Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.Sqrt(4 - 3 * h)));
        }

        /// <summary>
        /// 获取样条曲线长度
        /// </summary>
        private static double GetSplineLength(Spline spline)
        {
            try
            {
                // 使用参数化方法计算样条曲线长度
                double length = 0;
                int segments = 100; // 分段数
                
                for (int i = 0; i < segments; i++)
                {
                    double t1 = (double)i / segments;
                    double t2 = (double)(i + 1) / segments;
                    
                    var point1 = spline.GetPointAtParameter(t1);
                    var point2 = spline.GetPointAtParameter(t2);
                    
                    length += point1.DistanceTo(point2);
                }
                
                return length;
            }
            catch
            {
                return GetBoundingBoxDiagonal(spline);
            }
        }

        /// <summary>
        /// 获取实体边界框对角线长度
        /// </summary>
        private static double GetBoundingBoxDiagonal(Entity entity)
        {
            try
            {
                var extents = entity.GeometricExtents;
                return extents.MinPoint.DistanceTo(extents.MaxPoint);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 检查实体颜色是否匹配指定颜色
        /// </summary>
        public static bool IsColorMatch(Entity entity, string colorName)
        {
            try
            {
                if (!ColorMapping.ContainsKey(colorName))
                    return false;

                short targetColorIndex = ColorMapping[colorName];
                var entityColor = entity.Color;

                // 检查颜色索引
                if (entityColor.ColorMethod == ColorMethod.ByAci)
                {
                    return entityColor.ColorIndex == targetColorIndex;
                }
                
                // 检查RGB颜色
                if (entityColor.ColorMethod == ColorMethod.ByColor)
                {
                    var targetColor = Color.FromColorIndex(ColorMethod.ByAci, targetColorIndex);
                    return entityColor.ColorValue == targetColor.ColorValue;
                }

                // 检查图层颜色
                if (entityColor.ColorMethod == ColorMethod.ByLayer)
                {
                    using (var transaction = entity.Database.TransactionManager.TopTransaction)
                    {
                        var layer = transaction.GetObject(entity.LayerId, OpenMode.ForRead) as LayerTableRecord;
                        if (layer != null)
                        {
                            return layer.Color.ColorIndex == targetColorIndex;
                        }
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从选择集中筛选指定颜色的实体并计算最长最短边
        /// </summary>
        public static (double maxLength, double minLength) CalculateMinMaxLength(
            IEnumerable<Entity> entities, string colorName)
        {
            var matchingEntities = entities.Where(e => IsColorMatch(e, colorName)).ToList();
            
            if (!matchingEntities.Any())
                return (0, 0);

            var lengths = matchingEntities.Select(GetEntityLength).Where(l => l > 0).ToList();
            
            if (!lengths.Any())
                return (0, 0);

            return (lengths.Max(), lengths.Min());
        }

        /// <summary>
        /// 格式化尺寸字符串
        /// </summary>
        public static string FormatDimension(double maxLength, double minLength)
        {
            if (maxLength <= 0 || minLength <= 0)
                return "";

            // 四舍五入到整数
            int max = (int)Math.Round(maxLength);
            int min = (int)Math.Round(minLength);

            return $"{max}x{min}";
        }

        /// <summary>
        /// 获取所有支持的颜色名称
        /// </summary>
        public static string[] GetSupportedColors()
        {
            return ColorMapping.Keys.ToArray();
        }
    }
}
