# CAD插件IP地址验证功能说明

## 功能概述
本插件已集成IP地址验证机制，只允许IP地址为 `192.168.5.xxx` 网段的用户使用插件功能。

## 验证机制特点

### 1. 一次性验证
- 只在插件首次加载时验证一次（netload时）
- 验证结果会被缓存，后续使用不再重复验证
- 确保性能优化，不影响插件正常使用

### 2. 验证时机
- **插件加载时**：在 `PluginApplication.Initialize()` 方法中进行验证
- **命令执行时**：每个命令方法都会检查验证状态

### 3. 允许的网段
- 当前配置：`192.168.5.xxx`
- 支持多网卡环境（只要有一个网卡在允许范围内即可）
- 自动跳过回环接口和非活动网络接口

### 4. 验证失败处理
- 显示错误信息：**"文件已损坏"**
- 不暴露IP验证逻辑，保持隐蔽性
- 阻止所有插件功能的使用

## 测试方法

### 在CAD中测试
1. 使用 `netload` 命令加载 `CADFileSaver.dll`
2. 观察加载时的消息：
   - **验证通过**：显示 "=== CAD文件快速保存插件 v2.0 已加载 ==="
   - **验证失败**：显示 "文件已损坏"

3. 执行插件命令测试：
   - `QUICKSAVE` 或 `快速保存` 或 `QS`
   - `CADFILESAVER_INIT`

### 预期行为
- **IP在192.168.5.xxx网段**：插件正常加载和使用
- **IP不在允许网段**：显示"文件已损坏"，无法使用插件功能

## 技术实现

### 核心文件
- `Core/IpValidationManager.cs` - IP验证管理器
- `Core/PluginCommands.cs` - 修改后的插件命令类

### 验证逻辑
1. 获取所有活动网络接口
2. 检查每个接口的IPv4地址
3. 验证IP地址是否匹配 192.168.5.xxx 模式
4. 缓存验证结果

### 优化特性（v2.0.1）
- **轻量级验证**：命令方法中使用 `IsPluginUsable` 属性进行快速检查
- **简化结构**：减少命令方法中的验证代码，提高第三方工具兼容性
- **集中初始化**：在插件加载时进行完整验证，后续只做状态检查
- **性能优化**：避免重复的网络检查和变量声明

### 配置修改
如需修改允许的网段，请编辑 `IpValidationManager.cs` 中的：
```csharp
private static readonly byte[] AllowedNetworkSegment = { 192, 168, 5 };
```

## 维护说明

### 代码结构
- 验证逻辑完全封装在 `IpValidationManager` 类中
- 对现有功能代码侵入性最小
- 易于维护和扩展

### 扩展功能
- 支持多网段验证
- 可添加调试日志功能
- 可配置不同的错误信息

### 安全考虑
- 验证异常时默认失败
- 不在日志中暴露验证逻辑
- 错误信息具有隐蔽性

## 版本信息
- 插件版本：v2.0.1
- IP验证功能：已集成并优化
- 编译时间：2025-07-13
- 目标框架：.NET Framework 4.8
- 优化重点：提高第三方工具兼容性，简化命令方法结构
