using System;
using System.Windows.Forms;
using Autodesk.AutoCAD.Runtime;
using CADFileSaver.Forms;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Core
{
    /// <summary>
    /// CAD插件命令类
    /// </summary>
    public class PluginCommands
    {
        private static MainForm _mainForm;

        /// <summary>
        /// 快速保存命令
        /// </summary>
        [CommandMethod("QUICKSAVE", CommandFlags.Modal)]
        public static void QuickSave()
        {
            // 轻量级验证检查
            if (!IpValidationManager.IsPluginUsable) return;

            try
            {
                // 如果窗体已经存在且未被释放，则显示它
                if (_mainForm != null && !_mainForm.IsDisposed)
                {
                    _mainForm.Show();
                    _mainForm.BringToFront();
                    _mainForm.TopMost = true;
                    return;
                }

                // 创建新的窗体实例
                _mainForm = new MainForm();
                _mainForm.Show();
                _mainForm.BringToFront();
                _mainForm.TopMost = true;

                // 处理窗体关闭事件
                _mainForm.FormClosed += (sender, e) => {
                    _mainForm = null;
                };
            }
            catch (System.Exception ex)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n错误: {ex.Message}\n");
                }
                else
                {
                    MessageBox.Show($"启动插件时发生错误: {ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 快速保存命令（中文别名）
        /// </summary>
        [CommandMethod("快速保存", CommandFlags.Modal)]
        public static void QuickSaveChinese()
        {
            // IP验证在QuickSave方法中已经处理，这里直接调用
            QuickSave();
        }

        /// <summary>
        /// 文件保存命令（简短别名）
        /// </summary>
        [CommandMethod("QS", CommandFlags.Modal)]
        public static void QS()
        {
            // IP验证在QuickSave方法中已经处理，这里直接调用
            QuickSave();
        }

        /// <summary>
        /// 插件初始化
        /// </summary>
        [CommandMethod("CADFILESAVER_INIT", CommandFlags.Modal)]
        public static void Initialize()
        {
            // 轻量级验证检查
            if (!IpValidationManager.IsPluginUsable) return;

            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage("\n=== CAD文件快速保存插件 v2.0 ===\n");
                    doc.Editor.WriteMessage("命令列表:\n");
                    doc.Editor.WriteMessage("  QUICKSAVE 或 快速保存 或 QS - 打开快速保存窗体\n");
                    doc.Editor.WriteMessage("  CADFILESAVER_INIT - 显示插件信息\n");
                    doc.Editor.WriteMessage("  CADFILESAVER_COMPAT - 显示版本兼容性信息\n");
                    doc.Editor.WriteMessage("插件已成功加载!\n");
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"插件初始化失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 版本兼容性诊断命令
        /// </summary>
        [CommandMethod("CADFILESAVER_COMPAT", CommandFlags.Modal)]
        public static void ShowCompatibilityInfo()
        {
            // 轻量级验证检查
            if (!IpValidationManager.IsPluginUsable) return;

            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage("\n=== 版本兼容性诊断 ===\n");
                    var compatibilityReport = FileExporterFactory.GetCompatibilityReport();
                    doc.Editor.WriteMessage($"{compatibilityReport}\n");
                    doc.Editor.WriteMessage("========================\n");
                }
            }
            catch (System.Exception ex)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"\n兼容性检查失败: {ex.Message}\n");
            }
        }

        /// <summary>
        /// 插件卸载清理
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                if (_mainForm != null && !_mainForm.IsDisposed)
                {
                    _mainForm.Close();
                    _mainForm = null;
                }
            }
            catch (System.Exception ex)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"\n插件清理时发生错误: {ex.Message}\n");
            }
        }
    }

    /// <summary>
    /// 插件应用程序类
    /// </summary>
    public class PluginApplication : IExtensionApplication
    {
        /// <summary>
        /// 插件初始化
        /// </summary>
        public void Initialize()
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;

                // 在插件加载时进行完整的IP验证初始化
                if (!IpValidationManager.InitializePluginValidation(doc?.Editor))
                {
                    return; // 验证失败，阻止插件正常加载
                }

                // 验证通过，正常加载插件
                if (doc != null)
                {
                    doc.Editor.WriteMessage("\n=== CAD文件快速保存插件 v2.0 已加载 ===\n");
                    doc.Editor.WriteMessage("输入 QUICKSAVE 或 快速保存 或 QS 开始使用\n");

                    // 显示版本兼容性信息
                    try
                    {
                        var exporterInfo = FileExporterFactory.GetCurrentExporterInfo();
                        doc.Editor.WriteMessage($"兼容性: {exporterInfo}\n");
                    }
                    catch (System.Exception)
                    {
                        // 如果获取兼容性信息失败，不影响插件加载
                    }
                }

                // 注册应用程序域卸载事件
                AppDomain.CurrentDomain.DomainUnload += (sender, e) => {
                    PluginCommands.Cleanup();
                };
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"插件加载失败: {ex.Message}", "CAD文件快速保存插件",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 插件终止
        /// </summary>
        public void Terminate()
        {
            try
            {
                PluginCommands.Cleanup();

                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage("\nCAD文件快速保存插件已卸载\n");
            }
            catch (System.Exception ex)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"\n插件卸载时发生错误: {ex.Message}\n");
            }
        }
    }
}
