using System;
using Autodesk.AutoCAD.ApplicationServices;

namespace CADFileSaver.Core
{
    /// <summary>
    /// AutoCAD版本枚举
    /// </summary>
    public enum AutoCADVersion
    {
        Unknown = 0,
        CAD2014 = 2014,
        CAD2015 = 2015,
        CAD2016 = 2016,
        CAD2017 = 2017,
        CAD2018 = 2018,
        CAD2019 = 2019,
        CAD2020 = 2020,
        CAD2021 = 2021,
        CAD2022 = 2022,
        CAD2023 = 2023,
        CAD2024 = 2024,
        CAD2025 = 2025
    }

    /// <summary>
    /// AutoCAD版本检测器
    /// 负责检测当前运行的AutoCAD版本，并判断需要使用哪种兼容性方案
    /// </summary>
    public static class VersionDetector
    {
        private static AutoCADVersion? _cachedVersion = null;
        private static bool? _isLegacyVersion = null;

        /// <summary>
        /// 获取当前AutoCAD版本
        /// </summary>
        /// <returns>AutoCAD版本枚举</returns>
        public static AutoCADVersion GetCurrentVersion()
        {
            if (_cachedVersion.HasValue)
            {
                return _cachedVersion.Value;
            }

            try
            {
                // 方法1：通过Application.Version获取版本信息
                var versionInfo = Application.Version;
                _cachedVersion = ParseVersionFromString(versionInfo.ToString());
                
                if (_cachedVersion == AutoCADVersion.Unknown)
                {
                    // 方法2：通过程序集版本推断
                    _cachedVersion = DetectVersionFromAssembly();
                }

                return _cachedVersion.Value;
            }
            catch (Exception)
            {
                _cachedVersion = AutoCADVersion.Unknown;
                return _cachedVersion.Value;
            }
        }

        /// <summary>
        /// 判断当前版本是否为需要兼容处理的老版本（CAD 2014-2017）
        /// </summary>
        /// <returns>如果是老版本返回true，否则返回false</returns>
        public static bool IsLegacyVersion()
        {
            if (_isLegacyVersion.HasValue)
            {
                return _isLegacyVersion.Value;
            }

            var currentVersion = GetCurrentVersion();
            _isLegacyVersion = currentVersion >= AutoCADVersion.CAD2014 && currentVersion <= AutoCADVersion.CAD2017;
            
            return _isLegacyVersion.Value;
        }

        /// <summary>
        /// 判断当前版本是否为现代版本（CAD 2018+）
        /// </summary>
        /// <returns>如果是现代版本返回true，否则返回false</returns>
        public static bool IsModernVersion()
        {
            var currentVersion = GetCurrentVersion();
            return currentVersion >= AutoCADVersion.CAD2018;
        }

        /// <summary>
        /// 获取版本信息字符串（用于调试和日志）
        /// </summary>
        /// <returns>版本信息字符串</returns>
        public static string GetVersionInfo()
        {
            var version = GetCurrentVersion();
            var versionType = IsLegacyVersion() ? "Legacy" : "Modern";
            return $"AutoCAD {version} ({versionType})";
        }

        /// <summary>
        /// 重置缓存的版本信息（用于测试）
        /// </summary>
        internal static void ResetCache()
        {
            _cachedVersion = null;
            _isLegacyVersion = null;
        }

        #region 私有方法

        /// <summary>
        /// 从版本字符串解析AutoCAD版本
        /// </summary>
        /// <param name="versionString">版本字符串</param>
        /// <returns>解析出的版本枚举</returns>
        private static AutoCADVersion ParseVersionFromString(string versionString)
        {
            if (string.IsNullOrEmpty(versionString))
                return AutoCADVersion.Unknown;

            try
            {
                // AutoCAD版本字符串通常包含年份信息
                if (versionString.Contains("2025")) return AutoCADVersion.CAD2025;
                if (versionString.Contains("2024")) return AutoCADVersion.CAD2024;
                if (versionString.Contains("2023")) return AutoCADVersion.CAD2023;
                if (versionString.Contains("2022")) return AutoCADVersion.CAD2022;
                if (versionString.Contains("2021")) return AutoCADVersion.CAD2021;
                if (versionString.Contains("2020")) return AutoCADVersion.CAD2020;
                if (versionString.Contains("2019")) return AutoCADVersion.CAD2019;
                if (versionString.Contains("2018")) return AutoCADVersion.CAD2018;
                if (versionString.Contains("2017")) return AutoCADVersion.CAD2017;
                if (versionString.Contains("2016")) return AutoCADVersion.CAD2016;
                if (versionString.Contains("2015")) return AutoCADVersion.CAD2015;
                if (versionString.Contains("2014")) return AutoCADVersion.CAD2014;

                // 尝试从版本号推断（如 "24.2" 对应 2024）
                var parts = versionString.Split('.');
                if (parts.Length > 0 && int.TryParse(parts[0], out int majorVersion))
                {
                    return MapMajorVersionToCADVersion(majorVersion);
                }

                return AutoCADVersion.Unknown;
            }
            catch (Exception)
            {
                return AutoCADVersion.Unknown;
            }
        }

        /// <summary>
        /// 通过程序集版本检测AutoCAD版本
        /// </summary>
        /// <returns>检测到的版本</returns>
        private static AutoCADVersion DetectVersionFromAssembly()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetAssembly(typeof(Application));
                if (assembly != null)
                {
                    var version = assembly.GetName().Version;
                    if (version != null)
                    {
                        return MapMajorVersionToCADVersion(version.Major);
                    }
                }
                return AutoCADVersion.Unknown;
            }
            catch (Exception)
            {
                return AutoCADVersion.Unknown;
            }
        }

        /// <summary>
        /// 将主版本号映射到AutoCAD版本
        /// </summary>
        /// <param name="majorVersion">主版本号</param>
        /// <returns>对应的AutoCAD版本</returns>
        private static AutoCADVersion MapMajorVersionToCADVersion(int majorVersion)
        {
            // AutoCAD版本号映射关系
            switch (majorVersion)
            {
                case 19: return AutoCADVersion.CAD2014; // AutoCAD 2014/2015
                case 20: return AutoCADVersion.CAD2016; // AutoCAD 2016/2017
                case 21: return AutoCADVersion.CAD2017;
                case 22: return AutoCADVersion.CAD2018;
                case 23: return AutoCADVersion.CAD2019;
                case 24: return AutoCADVersion.CAD2020; // 可能需要进一步细分
                case 25: return AutoCADVersion.CAD2025;
                default:
                    // 对于未知版本，如果版本号较高，假设为现代版本
                    return majorVersion >= 22 ? AutoCADVersion.CAD2018 : AutoCADVersion.Unknown;
            }
        }

        #endregion
    }
}
