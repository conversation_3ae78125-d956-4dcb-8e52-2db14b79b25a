using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Core
{
    /// <summary>
    /// 文件操作辅助类
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 支持的文件格式映射
        /// </summary>
        private static readonly Dictionary<string, DwgVersion> FileFormatMapping = new Dictionary<string, DwgVersion>
        {
            { "AutoCAD 2018 DWG (*.dwg)", DwgVersion.Newest },
            { "AutoCAD 2013 DWG (*.dwg)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DWG (*.dwg)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DWG (*.dwg)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DWG (*.dwg)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DWG (*.dwg)", DwgVersion.AC1014 },
            { "AutoCAD 2018 DXF (*.dxf)", DwgVersion.Newest },
            { "AutoCAD 2013 DXF (*.dxf)", DwgVersion.AC1027 },
            { "AutoCAD 2010 DXF (*.dxf)", DwgVersion.AC1024 },
            { "AutoCAD 2007/LT2007 DXF (*.dxf)", DwgVersion.AC1021 },
            { "AutoCAD 2004/LT2004 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD 2000/LT2000 DXF (*.dxf)", DwgVersion.AC1015 },
            { "AutoCAD R14/LT98/LT97 DXF (*.dxf)", DwgVersion.AC1014 }
        };

        /// <summary>
        /// 生成文件名
        /// </summary>
        public static string GenerateFileName(string orderNumber, string materialName, 
            string remark, string serialNumber, string dimension)
        {
            var parts = new List<string>();
            
            // 添加非空的部分
            if (!string.IsNullOrWhiteSpace(orderNumber))
                parts.Add(orderNumber.Trim());
            
            if (!string.IsNullOrWhiteSpace(materialName))
                parts.Add(materialName.Trim());
            
            if (!string.IsNullOrWhiteSpace(remark))
                parts.Add(remark.Trim());
            
            if (!string.IsNullOrWhiteSpace(serialNumber))
                parts.Add(serialNumber.Trim());
            
            if (!string.IsNullOrWhiteSpace(dimension))
                parts.Add(dimension.Trim());

            return string.Join("-", parts);
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        public static string GetFileExtension(string fileType)
        {
            if (fileType.Contains("DWG"))
                return ".dwg";
            else if (fileType.Contains("DXF"))
                return ".dxf";
            else
                return ".dwg"; // 默认
        }

        /// <summary>
        /// 获取DWG版本（使用版本兼容的导出器）
        /// </summary>
        public static DwgVersion GetDwgVersion(string fileType)
        {
            try
            {
                // 使用当前版本兼容的导出器获取DWG版本
                var exporter = FileExporterFactory.CreateExporter();
                return exporter.GetCompatibleDwgVersion(fileType);
            }
            catch (System.Exception)
            {
                // 如果获取失败，使用传统映射作为后备
                if (FileFormatMapping.ContainsKey(fileType))
                    return FileFormatMapping[fileType];

                return DwgVersion.AC1021; // 默认AutoCAD 2007
            }
        }

        /// <summary>
        /// 保存选择的实体到文件
        /// 使用版本兼容的导出器自动适配不同的AutoCAD版本
        /// </summary>
        public static bool SaveEntitiesToFile(ObjectIdCollection entityIds, string filePath,
            string fileType, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                // 使用工厂模式创建适合当前版本的文件导出器
                var exporter = FileExporterFactory.CreateExporter();

                // 检查导出器是否支持当前版本
                if (!exporter.IsVersionSupported())
                {
                    errorMessage = $"当前AutoCAD版本不受支持。版本信息: {VersionDetector.GetVersionInfo()}";
                    return false;
                }

                // 使用版本兼容的导出器保存文件
                return exporter.SaveEntitiesToFile(entityIds, filePath, fileType, out errorMessage);
            }
            catch (System.Exception ex)
            {
                // 提供详细的错误信息，包括版本兼容性信息
                var compatibilityReport = FileExporterFactory.GetCompatibilityReport();
                errorMessage = $"保存文件失败: {ex.Message}\n\n兼容性信息:\n{compatibilityReport}";
                return false;
            }
        }

        /// <summary>
        /// 验证文件路径
        /// </summary>
        public static bool IsValidPath(string path, out string errorMessage)
        {
            errorMessage = "";
            
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    errorMessage = "路径不能为空";
                    return false;
                }

                // 检查路径格式
                var fullPath = Path.GetFullPath(path);
                
                // 检查目录是否存在或可以创建
                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                    }
                    catch (Exception ex)
                    {
                        errorMessage = $"无法创建目录: {ex.Message}";
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"路径无效: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 获取所有支持的文件格式
        /// </summary>
        public static string[] GetSupportedFileFormats()
        {
            return FileFormatMapping.Keys.ToArray();
        }

        /// <summary>
        /// 清理文件名中的非法字符
        /// </summary>
        public static string CleanFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "未命名";

            // 移除或替换非法字符
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // 移除多余的空格和特殊字符
            fileName = fileName.Trim().Replace("  ", " ");
            
            // 限制文件名长度
            if (fileName.Length > 200)
            {
                fileName = fileName.Substring(0, 200);
            }

            return fileName;
        }
    }
}
