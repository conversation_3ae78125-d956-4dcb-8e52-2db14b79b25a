using System;
using System.Net;
using System.Net.NetworkInformation;
using Autodesk.AutoCAD.EditorInput;

namespace CADFileSaver.Core
{
    /// <summary>
    /// IP地址验证管理器
    /// 负责验证用户IP地址是否在允许的网段内
    /// 只在插件加载时验证一次，后续使用缓存结果
    /// </summary>
    internal static class IpValidationManager
    {
        #region 私有字段
        
        /// <summary>
        /// 是否已经进行过IP验证
        /// </summary>
        private static bool _isValidated = false;
        
        /// <summary>
        /// IP验证是否通过
        /// </summary>
        private static bool _isValidationPassed = false;
        
        /// <summary>
        /// 允许的网段配置 - 192.168.5.xxx
        /// 可以通过修改这些值来更改允许的网段
        /// </summary>
        private static readonly byte[] AllowedNetworkSegment = { 192, 168, 5 };
        
        /// <summary>
        /// 验证失败时显示的错误信息
        /// </summary>
        private const string ValidationFailedMessage = "文件已损坏";
        
        #endregion

        #region 公共方法

        /// <summary>
        /// 插件是否可用（IP验证通过且已初始化）
        /// 提供快速的状态检查，用于命令方法中的轻量级验证
        /// </summary>
        public static bool IsPluginUsable => _isValidated && _isValidationPassed;

        /// <summary>
        /// 验证当前用户的IP地址是否在允许的网段内
        /// 此方法只会在首次调用时执行实际验证，后续调用直接返回缓存结果
        /// </summary>
        /// <returns>如果IP地址验证通过返回true，否则返回false</returns>
        public static bool ValidateIpAddress()
        {
            try
            {
                // 如果已经验证过，直接返回缓存结果（性能优化）
                if (_isValidated)
                {
                    return _isValidationPassed;
                }

                // 执行实际的IP验证
                bool validationResult = PerformIpValidation();

                // 缓存验证结果
                _isValidated = true;
                _isValidationPassed = validationResult;

                return validationResult;
            }
            catch (Exception)
            {
                // 验证过程中出现任何异常，默认为验证失败
                _isValidated = true;
                _isValidationPassed = false;
                return false;
            }
        }
        
        /// <summary>
        /// 检查插件是否可以使用（IP验证通过）
        /// 如果验证失败，会通过编辑器显示错误信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象，用于显示错误信息</param>
        /// <returns>如果可以使用返回true，否则返回false</returns>
        public static bool CheckPluginUsage(Editor editor = null)
        {
            if (!ValidateIpAddress())
            {
                // 显示隐蔽的错误信息，不暴露IP验证逻辑
                editor?.WriteMessage($"\n{ValidationFailedMessage}\n");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 初始化插件验证状态
        /// 在插件加载时调用，执行完整的IP验证流程
        /// </summary>
        /// <param name="editor">CAD编辑器对象，用于显示消息</param>
        /// <returns>如果验证通过返回true，否则返回false</returns>
        public static bool InitializePluginValidation(Editor editor = null)
        {
            bool result = ValidateIpAddress();
            if (!result)
            {
                editor?.WriteMessage($"\n{ValidationFailedMessage}\n");
            }
            return result;
        }
        
        /// <summary>
        /// 获取当前验证状态（用于调试和测试）
        /// </summary>
        /// <returns>返回验证状态元组：(是否已验证, 验证是否通过)</returns>
        public static (bool IsValidated, bool IsPassed) GetValidationStatus()
        {
            return (_isValidated, _isValidationPassed);
        }
        
        #endregion

        #region 私有方法
        
        /// <summary>
        /// 执行实际的IP地址验证逻辑
        /// </summary>
        /// <returns>验证是否通过</returns>
        private static bool PerformIpValidation()
        {
            try
            {
                // 获取所有网络接口
                NetworkInterface[] networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
                
                foreach (NetworkInterface networkInterface in networkInterfaces)
                {
                    // 跳过非活动接口和回环接口
                    if (networkInterface.OperationalStatus != OperationalStatus.Up ||
                        networkInterface.NetworkInterfaceType == NetworkInterfaceType.Loopback)
                    {
                        continue;
                    }

                    // 获取IP属性
                    IPInterfaceProperties ipProperties = networkInterface.GetIPProperties();
                    
                    foreach (UnicastIPAddressInformation ipInfo in ipProperties.UnicastAddresses)
                    {
                        IPAddress ipAddress = ipInfo.Address;
                        
                        // 只检查IPv4地址
                        if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        {
                            // 检查是否在允许的网段内
                            if (IsIpInAllowedSegment(ipAddress))
                            {
                                return true; // 找到符合条件的IP地址
                            }
                        }
                    }
                }

                // 没有找到符合条件的IP地址
                return false;
            }
            catch (Exception)
            {
                // 网络检查过程中出现异常，默认验证失败
                return false;
            }
        }
        
        /// <summary>
        /// 检查指定IP地址是否在允许的网段内
        /// </summary>
        /// <param name="ipAddress">要检查的IP地址</param>
        /// <returns>如果在允许网段内返回true，否则返回false</returns>
        private static bool IsIpInAllowedSegment(IPAddress ipAddress)
        {
            try
            {
                byte[] addressBytes = ipAddress.GetAddressBytes();
                
                // 确保是IPv4地址（4个字节）
                if (addressBytes.Length != 4)
                {
                    return false;
                }
                
                // 检查前三个字节是否匹配允许的网段
                for (int i = 0; i < AllowedNetworkSegment.Length; i++)
                {
                    if (addressBytes[i] != AllowedNetworkSegment[i])
                    {
                        return false;
                    }
                }
                
                return true; // 所有网段字节都匹配
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        #endregion
        
        #region 配置和扩展方法
        
        /// <summary>
        /// 重置验证状态（仅用于测试和调试）
        /// 注意：在生产环境中不应该调用此方法
        /// </summary>
        internal static void ResetValidationState()
        {
            _isValidated = false;
            _isValidationPassed = false;
        }
        
        /// <summary>
        /// 获取当前允许的网段配置信息
        /// </summary>
        /// <returns>网段配置字符串</returns>
        public static string GetAllowedNetworkInfo()
        {
            return $"{AllowedNetworkSegment[0]}.{AllowedNetworkSegment[1]}.{AllowedNetworkSegment[2]}.xxx";
        }
        
        #endregion
    }
}
